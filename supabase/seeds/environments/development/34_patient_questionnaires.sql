-- Start transaction
BEGIN;

-- Insert Patient Questionnaires
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
)
INSERT INTO public.patient_questionnaires (
  id,
  patient_id,
  questionnaire_type,
  responses,
  completed_at,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  CASE p.name
    WHEN '<PERSON>' THEN 'health_history'
    WHEN '<PERSON>' THEN 'depression_screening'
    WHEN '<PERSON>' THEN 'asthma_control'
    WHEN '<PERSON>' THEN 'pain_assessment'
    WHEN '<PERSON>' THEN 'developmental_screening'
    ELSE 'health_history'
  END as questionnaire_type,
  CASE p.name
    WHEN '<PERSON>' THEN jsonb_build_object(
      'smoking_status', 'former',
      'alcohol_use', 'occasional',
      'exercise_frequency', '3-4 times per week',
      'family_history', jsonb_build_object(
        'diabetes', true,
        'heart_disease', true,
        'cancer', false
      ),
      'assigned_by', '<PERSON>',
      'assigned_date', (NOW() - INTERVAL '1 month')::text
    )
    WHEN '<PERSON>' THEN jsonb_build_object(
      'phq9_score', 4,
      'feeling_down', 'several_days',
      'sleep_issues', 'not_at_all',
      'fatigue', 'several_days',
      'appetite', 'not_at_all',
      'assigned_by', 'Sarah Johnson',
      'assigned_date', (NOW() - INTERVAL '2 weeks')::text
    )
    WHEN 'Sophia Martinez' THEN jsonb_build_object(
      'symptom_frequency', '1-2 days per week',
      'activity_limitation', 'minor',
      'nighttime_awakenings', '1-2 times per month',
      'rescue_inhaler_use', '1-2 days per week',
      'assigned_by', 'Michael Williams',
      'assigned_date', (NOW() - INTERVAL '3 weeks')::text
    )
    WHEN 'David Anderson' THEN jsonb_build_object(
      'assigned_by', 'Emily Davis',
      'assigned_date', (NOW() - INTERVAL '1 week')::text
    )
    WHEN 'Emma Garcia' THEN jsonb_build_object(
      'language_development', 'age_appropriate',
      'motor_skills', 'age_appropriate',
      'social_interaction', 'age_appropriate',
      'cognitive_development', 'age_appropriate',
      'assigned_by', 'Robert Brown',
      'assigned_date', (NOW() - INTERVAL '2 weeks')::text
    )
    ELSE jsonb_build_object(
      'assigned_by', 'System',
      'assigned_date', NOW()::text
    )
  END as responses,
  CASE p.name
    WHEN 'Alice Thompson' THEN NOW() - INTERVAL '3 weeks'
    WHEN 'James Wilson' THEN NOW() - INTERVAL '1 week'
    WHEN 'Sophia Martinez' THEN NOW() - INTERVAL '2 weeks'
    WHEN 'David Anderson' THEN NULL
    WHEN 'Emma Garcia' THEN NOW() - INTERVAL '1 week'
    ELSE NOW() - INTERVAL '1 day'
  END as completed_at,
  NOW(),
  NOW()
FROM patient_data p;

COMMIT;
