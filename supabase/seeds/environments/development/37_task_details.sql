-- Start transaction
BEGIN;

-- Insert Task Comments
WITH task_data AS (
  SELECT t.id, t.title, p.id as patient_id, p.first_name || ' ' || p.last_name AS patient_name
  FROM public.tasks t
  JOIN public.patients p ON (t.related_to->>'patient_id')::uuid = p.id
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.task_comments (
  id,
  task_id,
  user_id,
  content,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  t.id as task_id,
  p.user_id as user_id,
  CASE t.patient_name
    WHEN '<PERSON>' THEN 'Left voicemail for patient regarding lab results'
    WHEN '<PERSON>' THEN 'Echocardiogram results received, need to review before next appointment'
    WHEN '<PERSON>' THEN 'Updated vaccination record with recent immunizations'
    WHEN '<PERSON>' THEN 'Pharmacy confirmed prescription was received'
    WHEN '<PERSON>' THEN 'Scheduled follow-up appointment for next month'
  E<PERSON> as content,
  NOW() - INTERVAL '2 days',
  NOW() - INTERVAL '2 days'
FROM task_data t
JOIN provider_data p ON
  CASE t.patient_name
    WHEN '<PERSON>' THEN p.name = '<PERSON>'
    WHEN '<PERSON>' THEN p.name = 'Sarah <PERSON>'
    WHEN '<PERSON>' THEN p.name = '<PERSON> Williams'
    WHEN 'David Anderson' THEN p.name = 'Emily <PERSON>'
    WHEN 'Emma Garcia' THEN p.name = 'Robert Brown'
  END;

-- Insert Task Watchers
W<PERSON>H task_data AS (
  SELECT t.id, t.title, p.id as patient_id, p.first_name || ' ' || p.last_name AS patient_name
  FROM public.tasks t
  JOIN public.patients p ON (t.related_to->>'patient_id')::uuid = p.id
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.task_watchers (
  id,
  task_id,
  user_id,
  created_at
)
SELECT
  uuid_generate_v4(),
  t.id as task_id,
  p.user_id as user_id,
  NOW() - INTERVAL '1 week'
FROM task_data t
JOIN provider_data p ON
  CASE t.patient_name
    WHEN 'Alice Thompson' THEN p.name IN ('John Smith', 'Sarah Johnson')
    WHEN 'James Wilson' THEN p.name IN ('Sarah Johnson', 'John Smith')
    WHEN 'Sophia Martinez' THEN p.name IN ('Michael Williams', 'Emily Davis')
    WHEN 'David Anderson' THEN p.name IN ('Emily Davis', 'John Smith')
    WHEN 'Emma Garcia' THEN p.name IN ('Robert Brown', 'Michael Williams')
  END;

COMMIT;
