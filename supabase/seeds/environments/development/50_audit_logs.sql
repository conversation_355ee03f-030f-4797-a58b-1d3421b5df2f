-- Start transaction
BEGIN;

-- Insert Audit Logs
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.audit_logs (
  id,
  table_name,
  record_id,
  action,
  old_data,
  new_data,
  changed_by,
  timestamp
)
SELECT
  uuid_generate_v4(),
  'patients',
  p.id as record_id,
  action,
  NULL as old_data,
  jsonb_build_object('details', details) as new_data,
  actor.user_id as changed_by,
  timestamp
FROM patient_data p
JOIN provider_data actor ON
  CASE p.name
    WHEN '<PERSON>' THEN actor.name = '<PERSON>'
    WHEN '<PERSON>' THEN actor.name = '<PERSON>'
    WHEN '<PERSON>' THEN actor.name = '<PERSON>'
    WHEN '<PERSON>' THEN actor.name = '<PERSON>'
    WHEN '<PERSON>' THEN actor.name = '<PERSON>'
  END
CROSS JOIN (
  VALUES
    ('view', '{"section": "medical_history"}', NOW() - INTERVAL '1 day'),
    ('update', '{"section": "medications", "changes": {"added": ["Lisinopril 10mg"]}}', NOW() - INTERVAL '2 days'),
    ('view', '{"section": "lab_results"}', NOW() - INTERVAL '3 days'),
    ('create', '{"section": "clinical_notes", "note_type": "progress_note"}', NOW() - INTERVAL '4 days'),
    ('view', '{"section": "vital_signs"}', NOW() - INTERVAL '5 days')
) AS log_data(action, details, timestamp)
WHERE actor.user_id IS NOT NULL;

COMMIT;
