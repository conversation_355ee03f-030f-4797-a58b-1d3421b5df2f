-- Start transaction
BEGIN;

-- Insert Orders
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.orders (
  id,
  patient_id,
  ordering_provider_id,
  order_type,
  status,
  priority,
  order_details,
  diagnosis_codes,
  notes,
  ordered_at,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  prov.id as ordering_provider_id,
  CASE p.name
    WHEN '<PERSON>' THEN 'lab'::order_type
    WHEN '<PERSON>' THEN 'imaging'::order_type
    WHEN '<PERSON>' THEN 'lab'::order_type
    WHEN '<PERSON>' THEN 'imaging'::order_type
    WHEN '<PERSON>' THEN 'lab'::order_type
  END as order_type,
  CASE p.name
    WHEN '<PERSON>' THEN 'pending'::order_status
    WHEN '<PERSON>' THEN 'pending'::order_status
    WHEN '<PERSON>' THEN 'completed'::order_status
    WHEN '<PERSON>' THEN 'pending'::order_status
    WHEN '<PERSON>' THEN 'pending'::order_status
  END as status,
  CASE p.name
    WHEN '<PERSON>' THEN 'routine'::order_priority
    WHEN '<PERSON>' THEN 'routine'::order_priority
    WHEN 'Sophia Martinez' THEN 'routine'::order_priority
    WHEN 'David Anderson' THEN 'urgent'::order_priority
    WHEN 'Emma Garcia' THEN 'routine'::order_priority
  END as priority,
  CASE p.name
    WHEN 'Alice Thompson' THEN '{"test": "Complete Blood Count", "instructions": "Fasting for 8 hours"}'::jsonb
    WHEN 'James Wilson' THEN '{"test": "Echocardiogram", "instructions": "Evaluate heart function"}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"test": "Strep Test", "instructions": "Throat culture"}'::jsonb
    WHEN 'David Anderson' THEN '{"test": "Chest X-ray", "instructions": "Evaluate for pneumonia"}'::jsonb
    WHEN 'Emma Garcia' THEN '{"test": "Allergy Panel", "instructions": "Test for common food allergies"}'::jsonb
  END as order_details,
  CASE p.name
    WHEN 'Alice Thompson' THEN '{"codes": ["I10", "E11.9"]}'::jsonb
    WHEN 'James Wilson' THEN '{"codes": ["I25.10"]}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"codes": ["J02.9"]}'::jsonb
    WHEN 'David Anderson' THEN '{"codes": ["J18.9"]}'::jsonb
    WHEN 'Emma Garcia' THEN '{"codes": ["Z01.82"]}'::jsonb
  END as diagnosis_codes,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'Annual blood work'
    WHEN 'James Wilson' THEN 'Follow-up for CAD'
    WHEN 'Sophia Martinez' THEN 'Sore throat, completed test'
    WHEN 'David Anderson' THEN 'Persistent cough, fever'
    WHEN 'Emma Garcia' THEN 'Suspected egg allergy'
  END as notes,
  CASE p.name
    WHEN 'Alice Thompson' THEN NOW() - INTERVAL '2 days'
    WHEN 'James Wilson' THEN NOW() - INTERVAL '1 week'
    WHEN 'Sophia Martinez' THEN NOW() - INTERVAL '3 weeks'
    WHEN 'David Anderson' THEN NOW() - INTERVAL '1 day'
    WHEN 'Emma Garcia' THEN NOW() - INTERVAL '3 days'
  END as ordered_at,
  NOW(),
  NOW()
FROM patient_data p
JOIN provider_data prov ON
  CASE p.name
    WHEN 'Alice Thompson' THEN prov.name = 'John Smith'
    WHEN 'James Wilson' THEN prov.name = 'Sarah Johnson'
    WHEN 'Sophia Martinez' THEN prov.name = 'Michael Williams'
    WHEN 'David Anderson' THEN prov.name = 'Emily Davis'
    WHEN 'Emma Garcia' THEN prov.name = 'Robert Brown'
  END;

COMMIT;
