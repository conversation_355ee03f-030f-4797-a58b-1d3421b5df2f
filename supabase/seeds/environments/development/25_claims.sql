-- Start transaction
BEGIN;

-- Insert Claims
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name, organization_id
  FROM public.patients
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
),
insurance_data AS (
  SELECT id, name
  FROM public.insurance_providers
)
INSERT INTO public.claims (
  id,
  patient_id,
  provider_id,
  insurance_provider_id,
  service_date,
  billing_codes,
  status,
  amount,
  submitted_at,
  processed_at,
  metadata,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.id as patient_id,
  prov.id as provider_id,
  ins.id as insurance_provider_id,
  CASE p.name
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 month'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 weeks'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '3 months'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '1 week'
    WHEN '<PERSON>' THEN NOW() - INTERVAL '2 months'
  END::date as service_date,
  CASE p.name
    WHEN '<PERSON>' THEN jsonb_build_object(
      'diagnosis', ARRAY['I10', 'E11.9'],
      'procedures', ARRAY['99213', '85025']
    )
    WHEN 'James Wilson' THEN jsonb_build_object(
      'diagnosis', ARRAY['I25.10', 'I10'],
      'procedures', ARRAY['99214', '93306']
    )
    WHEN 'Sophia Martinez' THEN jsonb_build_object(
      'diagnosis', ARRAY['J02.9'],
      'procedures', ARRAY['99213', '87880']
    )
    WHEN 'David Anderson' THEN jsonb_build_object(
      'diagnosis', ARRAY['J18.9'],
      'procedures', ARRAY['99214', '71045']
    )
    WHEN 'Emma Garcia' THEN jsonb_build_object(
      'diagnosis', ARRAY['Z01.82'],
      'procedures', ARRAY['99213', '95004']
    )
  END as billing_codes,
  CASE p.name
    WHEN 'Alice Thompson' THEN 'submitted'
    WHEN 'James Wilson' THEN 'pending'
    WHEN 'Sophia Martinez' THEN 'processed'
    WHEN 'David Anderson' THEN 'denied'
    WHEN 'Emma Garcia' THEN 'submitted'
  END as status,
  CASE p.name
    WHEN 'Alice Thompson' THEN 250.00
    WHEN 'James Wilson' THEN 1200.00
    WHEN 'Sophia Martinez' THEN 150.00
    WHEN 'David Anderson' THEN 350.00
    WHEN 'Emma Garcia' THEN 175.00
  END as amount,
  CASE p.name
    WHEN 'Alice Thompson' THEN NOW() - INTERVAL '3 weeks'
    WHEN 'James Wilson' THEN NULL
    WHEN 'Sophia Martinez' THEN NOW() - INTERVAL '2 months'
    WHEN 'David Anderson' THEN NOW() - INTERVAL '6 days'
    WHEN 'Emma Garcia' THEN NOW() - INTERVAL '1 week'
  END as submitted_at,
  CASE p.name
    WHEN 'Alice Thompson' THEN NULL
    WHEN 'James Wilson' THEN NULL
    WHEN 'Sophia Martinez' THEN NOW() - INTERVAL '1 month'
    WHEN 'David Anderson' THEN NOW() - INTERVAL '2 days'
    WHEN 'Emma Garcia' THEN NULL
  END as processed_at,
  CASE p.name
    WHEN 'Alice Thompson' THEN '{"payment_method": "insurance"}'::jsonb
    WHEN 'James Wilson' THEN '{"payment_method": "insurance"}'::jsonb
    WHEN 'Sophia Martinez' THEN '{"payment_method": "insurance", "copay": 30.00}'::jsonb
    WHEN 'David Anderson' THEN '{"payment_method": "insurance", "denial_reason": "not covered"}'::jsonb
    WHEN 'Emma Garcia' THEN '{"payment_method": "insurance"}'::jsonb
  END as metadata,
  NOW(),
  NOW()
FROM patient_data p
JOIN provider_data prov ON
  CASE p.name
    WHEN 'Alice Thompson' THEN prov.name = 'John Smith'
    WHEN 'James Wilson' THEN prov.name = 'Sarah Johnson'
    WHEN 'Sophia Martinez' THEN prov.name = 'Michael Williams'
    WHEN 'David Anderson' THEN prov.name = 'Emily Davis'
    WHEN 'Emma Garcia' THEN prov.name = 'Robert Brown'
  END
JOIN insurance_data ins ON
  CASE p.name
    WHEN 'Alice Thompson' THEN ins.name = 'Blue Cross Blue Shield'
    WHEN 'James Wilson' THEN ins.name = 'Aetna'
    WHEN 'Sophia Martinez' THEN ins.name = 'United Healthcare'
    WHEN 'David Anderson' THEN ins.name = 'Cigna'
    WHEN 'Emma Garcia' THEN ins.name = 'Humana'
  END;

COMMIT;
