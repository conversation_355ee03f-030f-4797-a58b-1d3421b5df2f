-- Start transaction
BEGIN;

-- Insert Workflow Instances
WITH workflow_data AS (
  SELECT id, name
  FROM public.workflows
),
patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
)
INSERT INTO public.workflow_instances (
  id,
  workflow_id,
  status,
  current_step,
  context,
  started_at,
  completed_at,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  w.id as workflow_id,
  CASE
    WHEN w.name = 'New Patient Registration' AND p.name = '<PERSON>' THEN 'completed'::workflow_status
    WHEN w.name = 'Annual Physical Exam' AND p.name = '<PERSON>' THEN 'in_progress'::workflow_status
    WHEN w.name = 'Prescription Refill' AND p.name = '<PERSON>' THEN 'completed'::workflow_status
    WHEN w.name = 'Lab Results Review' AND p.name = '<PERSON>' THEN 'in_progress'::workflow_status
    WHEN w.name = 'Specialist Referral' AND p.name = '<PERSON>' THEN 'pending'::workflow_status
  END as status,
  CASE
    WHEN w.name = 'New Patient Registration' AND p.name = '<PERSON>' THEN 5
    WHEN w.name = 'Annual Physical Exam' AND p.name = '<PERSON>' THEN 3
    WHEN w.name = 'Prescription Refill' AND p.name = '<PERSON> Martinez' THEN 4
    WHEN w.name = 'Lab Results Review' AND p.name = 'David <PERSON>' THEN 2
    WHEN w.name = 'Specialist Referral' AND p.name = 'Emma Garcia' THEN 1
  END as current_step,
  jsonb_build_object('patient_id', p.id) as context,
  CASE
    WHEN w.name = 'New Patient Registration' AND p.name = 'Alice Thompson' THEN NOW() - INTERVAL '6 months'
    WHEN w.name = 'Annual Physical Exam' AND p.name = 'James Wilson' THEN NOW() - INTERVAL '1 week'
    WHEN w.name = 'Prescription Refill' AND p.name = 'Sophia Martinez' THEN NOW() - INTERVAL '2 weeks'
    WHEN w.name = 'Lab Results Review' AND p.name = 'David Anderson' THEN NOW() - INTERVAL '3 days'
    WHEN w.name = 'Specialist Referral' AND p.name = 'Emma Garcia' THEN NOW() - INTERVAL '1 day'
  END as started_at,
  CASE
    WHEN w.name = 'New Patient Registration' AND p.name = 'Alice Thompson' THEN NOW() - INTERVAL '5 months'
    WHEN w.name = 'Annual Physical Exam' AND p.name = 'James Wilson' THEN NULL
    WHEN w.name = 'Prescription Refill' AND p.name = 'Sophia Martinez' THEN NOW() - INTERVAL '1 week'
    WHEN w.name = 'Lab Results Review' AND p.name = 'David Anderson' THEN NULL
    WHEN w.name = 'Specialist Referral' AND p.name = 'Emma Garcia' THEN NULL
  END as completed_at,
  NOW(),
  NOW()
FROM workflow_data w
CROSS JOIN patient_data p
WHERE (w.name = 'New Patient Registration' AND p.name = 'Alice Thompson') OR
      (w.name = 'Annual Physical Exam' AND p.name = 'James Wilson') OR
      (w.name = 'Prescription Refill' AND p.name = 'Sophia Martinez') OR
      (w.name = 'Lab Results Review' AND p.name = 'David Anderson') OR
      (w.name = 'Specialist Referral' AND p.name = 'Emma Garcia');

-- Insert Workflow Logs
WITH workflow_instance_data AS (
  SELECT wi.id, wi.workflow_id, w.name as workflow_name, wi.current_step, wi.status,
    (wi.context->>'patient_id')::uuid as patient_id
  FROM public.workflow_instances wi
  JOIN public.workflows w ON wi.workflow_id = w.id
),
patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.workflow_logs (
  id,
  workflow_instance_id,
  step_number,
  step_name,
  status,
  message,
  details,
  created_at
)
SELECT
  uuid_generate_v4(),
  wi.id as workflow_instance_id,
  step_number,
  step_name,
  log_data.status,
  message,
  jsonb_build_object(
    'performed_by', prov.user_id,
    'performed_at', performed_at
  ) as details,
  NOW()
FROM workflow_instance_data wi
JOIN patient_data p ON wi.patient_id = p.id
JOIN provider_data prov ON
  CASE p.name
    WHEN 'Alice Thompson' THEN prov.name = 'John Smith'
    WHEN 'James Wilson' THEN prov.name = 'Sarah Johnson'
    WHEN 'Sophia Martinez' THEN prov.name = 'Michael Williams'
    WHEN 'David Anderson' THEN prov.name = 'Emily Davis'
    WHEN 'Emma Garcia' THEN prov.name = 'Robert Brown'
  END
CROSS JOIN (
  VALUES
    (1, 'Patient Registration', 'completed'::workflow_status, 'Workflow initiated', NOW() - INTERVAL '6 months'),
    (1, 'Information Collection', 'completed'::workflow_status, 'Step 1 completed', NOW() - INTERVAL '5 months 3 weeks'),
    (2, 'Insurance Verification', 'completed'::workflow_status, 'Step 2 started', NOW() - INTERVAL '5 months 3 weeks'),
    (2, 'Insurance Verification', 'completed'::workflow_status, 'Step 2 completed', NOW() - INTERVAL '5 months 2 weeks'),
    (3, 'Medical History Review', 'completed'::workflow_status, 'Step 3 started', NOW() - INTERVAL '5 months 2 weeks'),
    (3, 'Medical History Review', 'completed'::workflow_status, 'Step 3 completed', NOW() - INTERVAL '5 months 1 week'),
    (4, 'Provider Assignment', 'completed'::workflow_status, 'Step 4 started', NOW() - INTERVAL '5 months 1 week'),
    (4, 'Provider Assignment', 'completed'::workflow_status, 'Step 4 completed', NOW() - INTERVAL '5 months'),
    (5, 'Appointment Scheduling', 'completed'::workflow_status, 'Step 5 started', NOW() - INTERVAL '5 months'),
    (5, 'Appointment Scheduling', 'completed'::workflow_status, 'Step 5 completed', NOW() - INTERVAL '5 months')
) AS log_data(step_number, step_name, status, message, performed_at)
WHERE (wi.workflow_name = 'New Patient Registration' AND p.name = 'Alice Thompson' AND log_data.step_number <= 5) OR
      (wi.workflow_name = 'Annual Physical Exam' AND p.name = 'James Wilson' AND log_data.step_number <= 3) OR
      (wi.workflow_name = 'Prescription Refill' AND p.name = 'Sophia Martinez' AND log_data.step_number <= 4) OR
      (wi.workflow_name = 'Lab Results Review' AND p.name = 'David Anderson' AND log_data.step_number <= 2) OR
      (wi.workflow_name = 'Specialist Referral' AND p.name = 'Emma Garcia' AND log_data.step_number <= 1);

COMMIT;
