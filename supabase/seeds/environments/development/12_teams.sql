-- Teams seed data
BEGIN;

-- Insert Teams using CTEs
WITH provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
),
department_data AS (
  SELECT d.id, d.name, f.name AS facility_name
  FROM public.departments d
  JOIN public.facilities f ON d.facility_id = f.id
)
INSERT INTO public.teams (
  id,
  name,
  description,
  department_id,
  leader_id
)
SELECT
  uuid_generate_v4(),
  team_name,
  team_description,
  dept.id,
  prov.id
FROM (
  VALUES
    ('Primary Care Team A', 'Primary care providers and staff', 'Primary Care', 'Spritely Main Hospital', '<PERSON> Smith'),
    ('Cardiology Team', 'Cardiology specialists and staff', 'Cardiology', 'Spritely Main Hospital', 'Sarah Johnson'),
    ('Pediatrics Team A', 'Pediatric providers and staff', 'Pediatrics', 'Spritely Main Hospital', 'Michael Williams'),
    ('Primary Care Team', 'Primary care providers and staff', 'Primary Care', 'Spritely Community Clinic', '<PERSON> Davis'),
    ('Pediatrics Team', 'Pediatric providers and staff', 'Pediatrics', 'Spritely Pediatric Center', '<PERSON>')
) AS team_data(team_name, team_description, dept_name, facility_name, provider_name)
JOIN department_data dept ON dept.name = team_data.dept_name AND dept.facility_name = team_data.facility_name
JOIN provider_data prov ON prov.name = team_data.provider_name;

COMMIT;
