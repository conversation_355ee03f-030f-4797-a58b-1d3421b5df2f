-- Start transaction
BEGIN;

-- Insert Conversations
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name, organization_id
  FROM public.patients
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.conversations (
  id,
  organization_id,
  title,
  type,
  metadata,
  created_by,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  p.organization_id,
  CASE p.name
    WHEN '<PERSON>' THEN 'Medication Question - <PERSON>'
    WHEN '<PERSON>' THEN 'Follow-up Appointment - <PERSON>'
    WHEN '<PERSON>' THEN 'Asthma Management - <PERSON>'
    WHEN '<PERSON>' THEN 'Test Results - <PERSON>'
    WHEN '<PERSON>' THEN 'Vaccination Schedule - <PERSON>'
  END as title,
  'direct'::conversation_type as type,
  jsonb_build_object(
    'status', 'active',
    'conversation_purpose', CASE p.name
      WHEN '<PERSON>' THEN 'medication_question'
      WHEN '<PERSON>' THEN 'appointment_followup'
      WHEN '<PERSON>' THEN 'condition_management'
      WHEN '<PERSON>' THEN 'test_results'
      WHEN '<PERSON>' THEN 'vaccination_schedule'
    END
  ) as metadata,
  (SELECT user_id FROM provider_data WHERE name = CASE p.name
    W<PERSON>EN '<PERSON>' THEN 'John Smith'
    WHEN 'James <PERSON>' THEN 'Sarah <PERSON>'
    WHEN 'Sophia Martinez' THEN 'Michael Williams'
    WHEN 'David <PERSON>' THEN 'Emily Davis'
    WHEN 'Emma Garcia' THEN 'Robert Brown'
  END) as created_by,
  NOW() - INTERVAL '1 week',
  NOW() - INTERVAL '1 day'
FROM patient_data p;

-- Get conversation IDs
WITH conversation_data AS (
  SELECT c.id, c.title,
    CASE
      WHEN c.title LIKE '%Alice Thompson%' THEN 'Alice Thompson'
      WHEN c.title LIKE '%James Wilson%' THEN 'James Wilson'
      WHEN c.title LIKE '%Sophia Martinez%' THEN 'Sophia Martinez'
      WHEN c.title LIKE '%David Anderson%' THEN 'David Anderson'
      WHEN c.title LIKE '%Emma Garcia%' THEN 'Emma Garcia'
    END as patient_name
  FROM public.conversations c
),
patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)

-- Insert Conversation Participants
-- Note: We're only adding provider participants since patients don't have auth users yet
INSERT INTO public.conversation_participants (
  id,
  conversation_id,
  user_id,
  role,
  last_read_at,
  created_at
)

SELECT
  uuid_generate_v4(),
  c.id as conversation_id,
  prov.user_id as user_id,
  'member' as role,
  NOW() - INTERVAL '1 week',
  NOW() - INTERVAL '1 week'
FROM conversation_data c
JOIN patient_data p ON c.patient_name = p.name
JOIN provider_data prov ON
  CASE p.name
    WHEN 'Alice Thompson' THEN prov.name = 'John Smith'
    WHEN 'James Wilson' THEN prov.name = 'Sarah Johnson'
    WHEN 'Sophia Martinez' THEN prov.name = 'Michael Williams'
    WHEN 'David Anderson' THEN prov.name = 'Emily Davis'
    WHEN 'Emma Garcia' THEN prov.name = 'Robert Brown'
  END;

-- Insert Messages
WITH conversation_data AS (
  SELECT c.id, c.title,
    CASE
      WHEN c.title LIKE '%Alice Thompson%' THEN 'Alice Thompson'
      WHEN c.title LIKE '%James Wilson%' THEN 'James Wilson'
      WHEN c.title LIKE '%Sophia Martinez%' THEN 'Sophia Martinez'
      WHEN c.title LIKE '%David Anderson%' THEN 'David Anderson'
      WHEN c.title LIKE '%Emma Garcia%' THEN 'Emma Garcia'
    END as patient_name
  FROM public.conversations c
),
patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
),
participant_data AS (
  SELECT
    cp.id as participant_id,
    cp.conversation_id,
    cp.user_id,
    CASE
      WHEN p.id IS NOT NULL THEN 'patient'
      WHEN prov.id IS NOT NULL THEN 'provider'
      ELSE NULL
    END as participant_type,
    COALESCE(p.name, prov.name) as name
  FROM public.conversation_participants cp
  LEFT JOIN patient_data p ON cp.user_id = p.id
  LEFT JOIN provider_data prov ON cp.user_id = prov.user_id
)

INSERT INTO public.messages (
  id,
  conversation_id,
  sender_id,
  content,
  created_at,
  updated_at
)
-- Skip patient messages since patients don't have auth users yet


-- Provider messages only
SELECT
  uuid_generate_v4(),
  pd.conversation_id,
  pd.user_id as sender_id,
  CASE pd.name
    WHEN 'John Smith' THEN 'Hello Alice, yes, it''s fine to take your medication in the evening. Just be consistent with the timing each day.'
    WHEN 'Sarah Johnson' THEN 'Hi James, let''s schedule your next appointment in 3 months. Please call the office to set that up.'
    WHEN 'Michael Williams' THEN 'Hello, increased inhaler use could indicate her asthma is not well controlled. Let''s schedule an appointment to reassess her treatment plan.'
    WHEN 'Emily Davis' THEN 'Hi David, your test results have just come in. Everything looks normal, but I''d like to discuss them at your next appointment.'
    WHEN 'Robert Brown' THEN 'Hello, I''ve attached Emma''s updated vaccination schedule. She''s due for her next set of vaccines in 3 months.'
  END as content,
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '5 days'
FROM participant_data pd
WHERE pd.participant_type = 'provider';

-- Insert Message States
WITH message_data AS (
  SELECT m.id as message_id, cp.user_id
  FROM public.messages m
  JOIN public.conversation_participants cp ON m.conversation_id = cp.conversation_id
  WHERE cp.user_id != m.sender_id
)
INSERT INTO public.message_states (
  id,
  message_id,
  user_id,
  state,
  updated_at
)
SELECT
  uuid_generate_v4(),
  md.message_id,
  md.user_id,
  'read'::message_state,
  NOW() - INTERVAL '1 day'
FROM message_data md;

COMMIT;
