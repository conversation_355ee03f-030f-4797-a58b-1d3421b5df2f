-- Start transaction
BEGIN;

-- Insert Immunizations
WITH patient_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.patients
  WHERE first_name || ' ' || last_name IN ('<PERSON>', '<PERSON>', '<PERSON>')
),
provider_data AS (
  SELECT id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
  WHERE first_name || ' ' || last_name IN ('<PERSON>', '<PERSON>', '<PERSON>')
),
immunization_data AS (
  SELECT
    p.id AS patient_id,
    prov.id AS provider_id,
    p.name AS patient_name,
    prov.name AS provider_name,
    CASE
      WHEN p.name = '<PERSON>' AND prov.name = '<PERSON>' THEN
        ARRAY['DTaP', 'MMR']
      WHEN p.name = '<PERSON>' AND prov.name = '<PERSON>' THEN
        ARRAY['Influenza', 'Hepatitis B']
      WHEN p.name = '<PERSON>' AND prov.name = '<PERSON>' THEN
        ARRAY['Pneumococcal']
      ELSE
        ARRAY[]::text[]
    END AS vaccines
  FROM patient_data p
  CROSS JOIN provider_data prov
  WHERE
    (p.name = '<PERSON>' AND prov.name = '<PERSON>') OR
    (p.name = '<PERSON>' AND prov.name = '<PERSON>') OR
    (p.name = 'Alice Thompson' AND prov.name = '<PERSON>')
),
unnested_data AS (
  SELECT
    patient_id,
    provider_id,
    patient_name,
    provider_name,
    vaccine,
    row_number() OVER (PARTITION BY patient_id, provider_id ORDER BY ordinality) AS vaccine_index
  FROM immunization_data,
  unnest(vaccines) WITH ORDINALITY AS t(vaccine, ordinality)
)
INSERT INTO public.immunizations (
  id,
  patient_id,
  administered_by,
  vaccine_name,
  dose_number,
  administered_date,
  manufacturer,
  lot_number,
  expiration_date,
  site,
  route,
  notes,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  patient_id,
  provider_id,
  vaccine,
  CASE
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'DTaP' THEN 4
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'MMR' THEN 1
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Influenza' THEN 1
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Hepatitis B' THEN 3
    WHEN patient_name = 'Alice Thompson' AND vaccine = 'Pneumococcal' THEN 1
  END as dose_number,
  CASE
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'DTaP' THEN NOW() - INTERVAL '6 months'
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'MMR' THEN NOW() - INTERVAL '1 year'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Influenza' THEN NOW() - INTERVAL '3 months'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Hepatitis B' THEN NOW() - INTERVAL '8 months'
    WHEN patient_name = 'Alice Thompson' AND vaccine = 'Pneumococcal' THEN NOW() - INTERVAL '2 years'
  END as administered_date,
  CASE
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'DTaP' THEN 'Sanofi Pasteur'
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'MMR' THEN 'Merck'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Influenza' THEN 'GlaxoSmithKline'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Hepatitis B' THEN 'Merck'
    WHEN patient_name = 'Alice Thompson' AND vaccine = 'Pneumococcal' THEN 'Pfizer'
  END as manufacturer,
  CASE
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'DTaP' THEN 'C0278BA'
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'MMR' THEN 'D1234MM'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Influenza' THEN 'FLU2023A'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Hepatitis B' THEN 'HB789XY'
    WHEN patient_name = 'Alice Thompson' AND vaccine = 'Pneumococcal' THEN 'PN456ZZ'
  END as lot_number,
  CASE
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'DTaP' THEN NOW() + INTERVAL '1 year'
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'MMR' THEN NOW() + INTERVAL '6 months'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Influenza' THEN NOW() + INTERVAL '3 months'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Hepatitis B' THEN NOW() + INTERVAL '1 year'
    WHEN patient_name = 'Alice Thompson' AND vaccine = 'Pneumococcal' THEN NOW() - INTERVAL '1 year'
  END as expiration_date,
  CASE
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'DTaP' THEN 'left_arm'::administration_site
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'MMR' THEN 'right_arm'::administration_site
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Influenza' THEN 'left_arm'::administration_site
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Hepatitis B' THEN 'right_thigh'::administration_site
    WHEN patient_name = 'Alice Thompson' AND vaccine = 'Pneumococcal' THEN 'left_arm'::administration_site
  END as site,
  'intramuscular'::administration_route as route,
  CASE
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'DTaP' THEN 'Fourth dose of DTaP series'
    WHEN patient_name = 'Sophia Martinez' AND vaccine = 'MMR' THEN 'First dose of MMR series'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Influenza' THEN 'Annual flu vaccine'
    WHEN patient_name = 'Emma Garcia' AND vaccine = 'Hepatitis B' THEN 'Final dose of Hep B series'
    WHEN patient_name = 'Alice Thompson' AND vaccine = 'Pneumococcal' THEN 'Adult pneumococcal vaccine'
  END as notes,
  NOW(),
  NOW()
FROM unnested_data;

COMMIT;
