create schema if not exists "analytics";


create extension if not exists "pg_trgm" with schema "extensions";


create type "public"."administration_route" as enum ('intramuscular', 'subcutaneous', 'intradermal', 'oral', 'nasal');

create type "public"."administration_site" as enum ('left_arm', 'right_arm', 'left_thigh', 'right_thigh', 'other');

create type "public"."alert_severity" as enum ('low', 'medium', 'high', 'critical');

create type "public"."alert_status" as enum ('active', 'inactive', 'resolved');

create type "public"."allergy_severity" as enum ('mild', 'moderate', 'severe', 'life_threatening');

create type "public"."allergy_status" as enum ('active', 'inactive', 'resolved');

create type "public"."appointment_status" as enum ('scheduled', 'checked_in', 'in_progress', 'completed', 'cancelled', 'no_show');

create type "public"."conversation_type" as enum ('direct', 'group', 'department', 'announcement');

create type "public"."department_type" as enum ('primary_care', 'pediatrics', 'cardiology', 'neurology', 'orthopedics', 'emergency', 'laboratory', 'pharmacy', 'radiology', 'billing', 'administration');

create type "public"."gender" as enum ('male', 'female', 'other', 'prefer_not_to_say');

create type "public"."material_format" as enum ('text', 'pdf', 'video', 'audio', 'interactive');

create type "public"."message_state" as enum ('sent', 'delivered', 'read');

create type "public"."notification_priority" as enum ('low', 'medium', 'high', 'urgent');

create type "public"."notification_status" as enum ('pending', 'sent', 'delivered', 'read', 'failed');

create type "public"."notification_type" as enum ('appointment_reminder', 'lab_result', 'prescription_update', 'medical_record_update', 'task_assignment', 'message', 'alert', 'system_update');

create type "public"."order_priority" as enum ('routine', 'urgent', 'stat', 'emergency');

create type "public"."order_status" as enum ('pending', 'approved', 'in_progress', 'completed', 'cancelled', 'declined');

create type "public"."order_type" as enum ('lab', 'imaging', 'medication', 'procedure', 'referral', 'consultation', 'other');

create type "public"."provider_type" as enum ('doctor', 'nurse', 'specialist', 'admin');

create type "public"."referral_priority" as enum ('routine', 'urgent', 'emergency');

create type "public"."referral_status" as enum ('pending', 'scheduled', 'completed', 'cancelled', 'declined');

create type "public"."task_priority" as enum ('low', 'medium', 'high', 'urgent');

create type "public"."task_status" as enum ('pending', 'in_progress', 'completed', 'cancelled', 'blocked');

create type "public"."user_role" as enum ('system_admin', 'org_admin', 'clinical_admin', 'physician', 'nurse_practitioner', 'registered_nurse', 'medical_assistant', 'front_desk', 'billing_staff', 'pharmacist', 'lab_technician', 'patient');

create type "public"."workflow_status" as enum ('pending', 'in_progress', 'completed', 'cancelled', 'failed');

create type "public"."workflow_trigger" as enum ('scheduled', 'event_based', 'manual');

create type "public"."workflow_type" as enum ('appointment_reminder', 'lab_result_notification', 'prescription_renewal', 'patient_followup', 'referral_management', 'insurance_verification', 'document_review');

create table "public"."activity_logs" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid not null,
    "user_id" uuid not null,
    "action_type" text not null,
    "resource_type" text not null,
    "resource_id" text not null,
    "details" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."activity_logs" enable row level security;

create table "public"."allergies" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "allergen" text not null,
    "reaction" text,
    "severity" allergy_severity not null,
    "onset_date" date,
    "status" allergy_status default 'active'::allergy_status,
    "reported_by" uuid,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."allergies" enable row level security;

create table "public"."analytics_events" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "event_type" text not null,
    "event_data" jsonb not null,
    "user_id" uuid,
    "timestamp" timestamp with time zone default CURRENT_TIMESTAMP,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."analytics_events" enable row level security;

create table "public"."analytics_metrics" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "metric_name" text not null,
    "metric_value" numeric not null,
    "dimensions" jsonb default '{}'::jsonb,
    "timestamp" timestamp with time zone default CURRENT_TIMESTAMP,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."analytics_metrics" enable row level security;

create table "public"."appointments" (
    "id" uuid not null default gen_random_uuid(),
    "patient_id" uuid,
    "provider_id" uuid,
    "appointment_date" timestamp with time zone not null,
    "duration_minutes" integer not null default 30,
    "status" appointment_status not null default 'scheduled'::appointment_status,
    "reason" text,
    "notes" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "organization_id" uuid,
    "department_id" uuid
);


alter table "public"."appointments" enable row level security;

create table "public"."audit_logs" (
    "id" uuid not null default uuid_generate_v4(),
    "table_name" text not null,
    "record_id" uuid not null,
    "action" text not null,
    "old_data" jsonb,
    "new_data" jsonb,
    "changed_by" uuid,
    "timestamp" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."audit_logs" enable row level security;

create table "public"."billing_codes" (
    "id" uuid not null default uuid_generate_v4(),
    "code" text not null,
    "description" text not null,
    "type" text not null,
    "effective_date" date not null,
    "end_date" date,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."billing_codes" enable row level security;

create table "public"."care_team_members" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "provider_id" uuid,
    "role" text not null,
    "start_date" date not null,
    "end_date" date,
    "primary_contact" boolean default false,
    "notes" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."care_team_members" enable row level security;

create table "public"."claims" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "provider_id" uuid,
    "insurance_provider_id" uuid,
    "service_date" date not null,
    "billing_codes" jsonb not null,
    "status" text not null,
    "amount" numeric not null,
    "submitted_at" timestamp with time zone,
    "processed_at" timestamp with time zone,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."claims" enable row level security;

create table "public"."clinical_notes" (
    "id" uuid not null default uuid_generate_v4(),
    "medical_record_id" uuid,
    "note_type" text not null,
    "content" text not null,
    "signed_by" uuid,
    "signed_at" timestamp with time zone,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."clinical_notes" enable row level security;

create table "public"."conversation_participants" (
    "id" uuid not null default uuid_generate_v4(),
    "conversation_id" uuid,
    "user_id" uuid,
    "role" text default 'member'::text,
    "last_read_at" timestamp with time zone,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."conversation_participants" enable row level security;

create table "public"."conversations" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "type" conversation_type not null,
    "title" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_by" uuid,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."conversations" enable row level security;

create table "public"."departments" (
    "id" uuid not null default uuid_generate_v4(),
    "facility_id" uuid,
    "name" text not null,
    "type" department_type not null,
    "settings" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."departments" enable row level security;

create table "public"."documents" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "patient_id" uuid,
    "document_type" text not null,
    "title" text not null,
    "description" text,
    "file_path" text not null,
    "mime_type" text not null,
    "metadata" jsonb default '{}'::jsonb,
    "created_by" uuid,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."documents" enable row level security;

create table "public"."education_materials" (
    "id" uuid not null default uuid_generate_v4(),
    "title" text not null,
    "content" text not null,
    "category" text not null,
    "language" text default 'en'::text,
    "format" material_format not null,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."education_materials" enable row level security;

create table "public"."facilities" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "name" text not null,
    "type" text not null,
    "address" jsonb not null,
    "contact_info" jsonb not null,
    "operating_hours" jsonb,
    "settings" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."facilities" enable row level security;

create table "public"."healthcare_providers" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid,
    "first_name" text not null,
    "last_name" text not null,
    "provider_type" provider_type not null,
    "specialization" text,
    "license_number" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "organization_id" uuid,
    "department_id" uuid,
    "role" user_role,
    "specialties" text[],
    "credentials" jsonb,
    "schedule_settings" jsonb,
    "permissions" jsonb
);


alter table "public"."healthcare_providers" enable row level security;

create table "public"."immunizations" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "vaccine_name" text not null,
    "vaccine_code" text,
    "dose_number" integer,
    "administered_date" date not null,
    "administered_by" uuid,
    "manufacturer" text,
    "lot_number" text,
    "expiration_date" date,
    "site" administration_site,
    "route" administration_route,
    "notes" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."immunizations" enable row level security;

create table "public"."insurance_providers" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "contact_info" jsonb not null,
    "settings" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."insurance_providers" enable row level security;

create table "public"."inventory_items" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "facility_id" uuid,
    "name" text not null,
    "type" text not null,
    "quantity" integer not null,
    "unit" text not null,
    "minimum_quantity" integer,
    "location" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."inventory_items" enable row level security;

create table "public"."inventory_transactions" (
    "id" uuid not null default uuid_generate_v4(),
    "item_id" uuid,
    "transaction_type" text not null,
    "quantity" integer not null,
    "performed_by" uuid,
    "reason" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."inventory_transactions" enable row level security;

create table "public"."lab_results" (
    "id" uuid not null default gen_random_uuid(),
    "patient_id" uuid,
    "provider_id" uuid,
    "test_name" text not null,
    "test_date" timestamp with time zone not null,
    "results" jsonb not null,
    "normal_range" jsonb,
    "notes" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."lab_results" enable row level security;

create table "public"."medical_records" (
    "id" uuid not null default gen_random_uuid(),
    "patient_id" uuid,
    "provider_id" uuid,
    "visit_date" timestamp with time zone not null,
    "chief_complaint" text,
    "diagnosis" text[],
    "treatment_plan" text,
    "notes" text,
    "attachments" jsonb,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "search_vector" tsvector,
    "organization_id" uuid,
    "department_id" uuid
);


alter table "public"."medical_records" enable row level security;

create table "public"."medications" (
    "id" uuid not null default gen_random_uuid(),
    "patient_id" uuid,
    "provider_id" uuid,
    "medication_name" text not null,
    "dosage" text not null,
    "frequency" text not null,
    "start_date" date not null,
    "end_date" date,
    "instructions" text,
    "active" boolean default true,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."medications" enable row level security;

create table "public"."message_states" (
    "id" uuid not null default uuid_generate_v4(),
    "message_id" uuid,
    "user_id" uuid,
    "state" message_state not null,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."message_states" enable row level security;

create table "public"."messages" (
    "id" uuid not null default uuid_generate_v4(),
    "conversation_id" uuid,
    "sender_id" uuid,
    "content" text not null,
    "attachments" jsonb default '[]'::jsonb,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."messages" enable row level security;

create table "public"."notification_preferences" (
    "id" uuid not null default uuid_generate_v4(),
    "user_id" uuid,
    "type" notification_type not null,
    "email_enabled" boolean default true,
    "sms_enabled" boolean default false,
    "push_enabled" boolean default true,
    "in_app_enabled" boolean default true,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."notification_preferences" enable row level security;

create table "public"."notification_templates" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "type" notification_type not null,
    "name" text not null,
    "subject_template" text not null,
    "content_template" text not null,
    "metadata_template" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."notification_templates" enable row level security;

create table "public"."notifications" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "type" notification_type not null,
    "priority" notification_priority not null default 'medium'::notification_priority,
    "status" notification_status not null default 'pending'::notification_status,
    "sender_id" uuid,
    "recipient_id" uuid,
    "title" text not null,
    "content" text not null,
    "metadata" jsonb default '{}'::jsonb,
    "scheduled_for" timestamp with time zone,
    "sent_at" timestamp with time zone,
    "read_at" timestamp with time zone,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."notifications" enable row level security;

create table "public"."orders" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "ordering_provider_id" uuid,
    "order_type" order_type not null,
    "status" order_status default 'pending'::order_status,
    "priority" order_priority default 'routine'::order_priority,
    "order_details" jsonb not null,
    "diagnosis_codes" jsonb,
    "notes" text,
    "ordered_at" timestamp with time zone not null default CURRENT_TIMESTAMP,
    "scheduled_date" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."orders" enable row level security;

create table "public"."organization_invites" (
    "id" uuid not null default uuid_generate_v4(),
    "email" text not null,
    "organization_id" uuid,
    "role" user_role not null,
    "status" text default 'pending'::text,
    "invited_by" uuid,
    "created_at" timestamp with time zone default now(),
    "expires_at" timestamp with time zone default (now() + '7 days'::interval),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."organization_invites" enable row level security;

create table "public"."organizations" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "type" text not null,
    "settings" jsonb default '{}'::jsonb,
    "subscription_tier" text,
    "billing_info" jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "owner_id" uuid
);


alter table "public"."organizations" enable row level security;

create table "public"."patient_alerts" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "alert_type" text not null,
    "description" text not null,
    "severity" alert_severity not null,
    "status" alert_status default 'active'::alert_status,
    "start_date" date not null,
    "end_date" date,
    "created_by" uuid,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."patient_alerts" enable row level security;

create table "public"."patient_education_records" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "material_id" uuid,
    "provider_id" uuid,
    "provided_date" timestamp with time zone not null default CURRENT_TIMESTAMP,
    "notes" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."patient_education_records" enable row level security;

create table "public"."patient_portal_settings" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "preferences" jsonb default '{}'::jsonb,
    "communication_settings" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."patient_portal_settings" enable row level security;

create table "public"."patient_questionnaires" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "questionnaire_type" text not null,
    "responses" jsonb not null,
    "completed_at" timestamp with time zone,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."patient_questionnaires" enable row level security;

create table "public"."patients" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid,
    "first_name" text not null,
    "last_name" text not null,
    "date_of_birth" date not null,
    "gender" gender not null,
    "phone" text,
    "email" text,
    "address" text,
    "emergency_contact" text,
    "insurance_info" jsonb,
    "medical_history" jsonb,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "search_vector" tsvector,
    "organization_id" uuid
);


alter table "public"."patients" enable row level security;

create table "public"."referrals" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "referring_provider_id" uuid,
    "referred_to_provider_id" uuid,
    "reason" text not null,
    "priority" referral_priority default 'routine'::referral_priority,
    "status" referral_status default 'pending'::referral_status,
    "referral_date" date not null,
    "scheduled_date" date,
    "completed_date" date,
    "notes" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."referrals" enable row level security;

create table "public"."role_definitions" (
    "role" user_role not null,
    "description" text not null,
    "base_permissions" jsonb not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


create table "public"."role_permissions" (
    "id" uuid not null default uuid_generate_v4(),
    "role" user_role not null,
    "resource" text not null,
    "actions" text[] not null,
    "conditions" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."role_permissions" enable row level security;

create table "public"."task_comments" (
    "id" uuid not null default uuid_generate_v4(),
    "task_id" uuid,
    "user_id" uuid,
    "content" text not null,
    "attachments" jsonb default '[]'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."task_comments" enable row level security;

create table "public"."task_watchers" (
    "id" uuid not null default uuid_generate_v4(),
    "task_id" uuid,
    "user_id" uuid,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."task_watchers" enable row level security;

create table "public"."tasks" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "department_id" uuid,
    "title" text not null,
    "description" text,
    "priority" task_priority not null default 'medium'::task_priority,
    "status" task_status not null default 'pending'::task_status,
    "assigned_to" uuid,
    "assigned_by" uuid,
    "due_date" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "related_to" jsonb default '{}'::jsonb,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."tasks" enable row level security;

create table "public"."teams" (
    "id" uuid not null default uuid_generate_v4(),
    "department_id" uuid,
    "name" text not null,
    "description" text,
    "leader_id" uuid,
    "members" jsonb default '[]'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."teams" enable row level security;

create table "public"."templates" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "name" text not null,
    "type" text not null,
    "content" text not null,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."templates" enable row level security;

create table "public"."user_roles" (
    "id" uuid not null default uuid_generate_v4(),
    "user_id" uuid not null,
    "organization_id" uuid,
    "role" user_role not null,
    "department_id" uuid,
    "custom_permissions" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "invitation_status" text default 'direct'::text
);


alter table "public"."user_roles" enable row level security;

create table "public"."vital_signs" (
    "id" uuid not null default uuid_generate_v4(),
    "patient_id" uuid,
    "recorded_by" uuid,
    "recorded_at" timestamp with time zone not null default CURRENT_TIMESTAMP,
    "blood_pressure_systolic" integer,
    "blood_pressure_diastolic" integer,
    "heart_rate" integer,
    "respiratory_rate" integer,
    "temperature" numeric,
    "temperature_unit" text default 'C'::text,
    "oxygen_saturation" integer,
    "height" numeric,
    "weight" numeric,
    "bmi" numeric,
    "pain_level" integer,
    "notes" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."vital_signs" enable row level security;

create table "public"."workflow_instances" (
    "id" uuid not null default uuid_generate_v4(),
    "workflow_id" uuid,
    "status" workflow_status not null default 'pending'::workflow_status,
    "context" jsonb not null,
    "current_step" integer default 0,
    "results" jsonb default '[]'::jsonb,
    "started_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "completed_at" timestamp with time zone,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."workflow_instances" enable row level security;

create table "public"."workflow_logs" (
    "id" uuid not null default uuid_generate_v4(),
    "workflow_instance_id" uuid,
    "step_number" integer not null,
    "step_name" text not null,
    "status" workflow_status not null,
    "message" text,
    "details" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."workflow_logs" enable row level security;

create table "public"."workflows" (
    "id" uuid not null default uuid_generate_v4(),
    "organization_id" uuid,
    "type" workflow_type not null,
    "name" text not null,
    "description" text,
    "trigger_type" workflow_trigger not null,
    "trigger_config" jsonb not null,
    "steps" jsonb[] not null,
    "enabled" boolean default true,
    "created_by" uuid,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."workflows" enable row level security;

CREATE UNIQUE INDEX activity_logs_pkey ON public.activity_logs USING btree (id);

CREATE UNIQUE INDEX allergies_pkey ON public.allergies USING btree (id);

CREATE UNIQUE INDEX analytics_events_pkey ON public.analytics_events USING btree (id);

CREATE UNIQUE INDEX analytics_metrics_pkey ON public.analytics_metrics USING btree (id);

CREATE UNIQUE INDEX appointments_pkey ON public.appointments USING btree (id);

CREATE UNIQUE INDEX audit_logs_pkey ON public.audit_logs USING btree (id);

CREATE UNIQUE INDEX billing_codes_pkey ON public.billing_codes USING btree (id);

CREATE UNIQUE INDEX care_team_members_pkey ON public.care_team_members USING btree (id);

CREATE UNIQUE INDEX claims_pkey ON public.claims USING btree (id);

CREATE UNIQUE INDEX clinical_notes_pkey ON public.clinical_notes USING btree (id);

CREATE UNIQUE INDEX conversation_participants_conversation_id_user_id_key ON public.conversation_participants USING btree (conversation_id, user_id);

CREATE UNIQUE INDEX conversation_participants_pkey ON public.conversation_participants USING btree (id);

CREATE UNIQUE INDEX conversations_pkey ON public.conversations USING btree (id);

CREATE UNIQUE INDEX departments_pkey ON public.departments USING btree (id);

CREATE UNIQUE INDEX documents_pkey ON public.documents USING btree (id);

CREATE UNIQUE INDEX education_materials_pkey ON public.education_materials USING btree (id);

CREATE UNIQUE INDEX facilities_pkey ON public.facilities USING btree (id);

CREATE UNIQUE INDEX healthcare_providers_pkey ON public.healthcare_providers USING btree (id);

CREATE INDEX idx_activity_logs_created_at ON public.activity_logs USING btree (created_at);

CREATE INDEX idx_activity_logs_organization_id ON public.activity_logs USING btree (organization_id);

CREATE INDEX idx_activity_logs_resource_type ON public.activity_logs USING btree (resource_type);

CREATE INDEX idx_activity_logs_user_id ON public.activity_logs USING btree (user_id);

CREATE INDEX idx_allergies_allergen ON public.allergies USING btree (allergen);

CREATE INDEX idx_allergies_patient_id ON public.allergies USING btree (patient_id);

CREATE INDEX idx_allergies_reported_by ON public.allergies USING btree (reported_by);

CREATE INDEX idx_allergies_severity ON public.allergies USING btree (severity);

CREATE INDEX idx_allergies_status ON public.allergies USING btree (status);

CREATE INDEX idx_analytics_events_org_type ON public.analytics_events USING btree (organization_id, event_type);

CREATE INDEX idx_analytics_events_timestamp_brin ON public.analytics_events USING brin ("timestamp");

CREATE INDEX idx_analytics_events_user_id ON public.analytics_events USING btree (user_id);

CREATE INDEX idx_analytics_metrics_org_metric ON public.analytics_metrics USING btree (organization_id, metric_name);

CREATE INDEX idx_analytics_metrics_timestamp_brin ON public.analytics_metrics USING brin ("timestamp");

CREATE INDEX idx_appointments_date_brin ON public.appointments USING brin (appointment_date) WITH (pages_per_range='32');

CREATE INDEX idx_appointments_department_id ON public.appointments USING btree (department_id);

CREATE INDEX idx_appointments_organization_id ON public.appointments USING btree (organization_id);

CREATE INDEX idx_appointments_patient_date ON public.appointments USING btree (patient_id, appointment_date);

CREATE INDEX idx_appointments_provider_date ON public.appointments USING btree (provider_id, appointment_date);

CREATE INDEX idx_appointments_provider_patient_date ON public.appointments USING btree (provider_id, patient_id, appointment_date);

CREATE INDEX idx_appointments_provider_status ON public.appointments USING btree (provider_id, status, appointment_date);

CREATE INDEX idx_appointments_status_date ON public.appointments USING btree (status, appointment_date);

CREATE INDEX idx_appointments_status_provider_date ON public.appointments USING btree (status, provider_id, appointment_date) WHERE (status = 'scheduled'::appointment_status);

CREATE INDEX idx_audit_logs_changed_by ON public.audit_logs USING btree (changed_by);

CREATE INDEX idx_audit_logs_record_id ON public.audit_logs USING btree (record_id);

CREATE INDEX idx_audit_logs_table_name ON public.audit_logs USING btree (table_name);

CREATE INDEX idx_audit_logs_timestamp ON public.audit_logs USING btree ("timestamp");

CREATE INDEX idx_billing_codes_code ON public.billing_codes USING btree (code);

CREATE INDEX idx_billing_codes_type ON public.billing_codes USING btree (type);

CREATE INDEX idx_care_team_members_patient_id ON public.care_team_members USING btree (patient_id);

CREATE INDEX idx_care_team_members_primary_contact ON public.care_team_members USING btree (primary_contact);

CREATE INDEX idx_care_team_members_provider_id ON public.care_team_members USING btree (provider_id);

CREATE INDEX idx_care_team_members_role ON public.care_team_members USING btree (role);

CREATE INDEX idx_claims_insurance_provider_id ON public.claims USING btree (insurance_provider_id);

CREATE INDEX idx_claims_patient_id ON public.claims USING btree (patient_id);

CREATE INDEX idx_claims_provider_id ON public.claims USING btree (provider_id);

CREATE INDEX idx_claims_service_date ON public.claims USING btree (service_date);

CREATE INDEX idx_claims_status ON public.claims USING btree (status);

CREATE INDEX idx_clinical_notes_medical_record_id ON public.clinical_notes USING btree (medical_record_id);

CREATE INDEX idx_clinical_notes_signed_by ON public.clinical_notes USING btree (signed_by);

CREATE INDEX idx_conversation_participants_user_id ON public.conversation_participants USING btree (user_id);

CREATE INDEX idx_conversations_created_by ON public.conversations USING btree (created_by);

CREATE INDEX idx_conversations_organization_id ON public.conversations USING btree (organization_id);

CREATE INDEX idx_departments_facility_id ON public.departments USING btree (facility_id);

CREATE INDEX idx_documents_created_by ON public.documents USING btree (created_by);

CREATE INDEX idx_documents_document_type ON public.documents USING btree (document_type);

CREATE INDEX idx_documents_organization_id ON public.documents USING btree (organization_id);

CREATE INDEX idx_documents_patient_id ON public.documents USING btree (patient_id);

CREATE INDEX idx_education_materials_category ON public.education_materials USING btree (category);

CREATE INDEX idx_education_materials_format ON public.education_materials USING btree (format);

CREATE INDEX idx_education_materials_language ON public.education_materials USING btree (language);

CREATE INDEX idx_facilities_organization_id ON public.facilities USING btree (organization_id);

CREATE INDEX idx_healthcare_providers_department_id ON public.healthcare_providers USING btree (department_id);

CREATE INDEX idx_healthcare_providers_organization_id ON public.healthcare_providers USING btree (organization_id);

CREATE INDEX idx_healthcare_providers_user_id ON public.healthcare_providers USING btree (user_id);

CREATE INDEX idx_immunizations_administered_by ON public.immunizations USING btree (administered_by);

CREATE INDEX idx_immunizations_administered_date ON public.immunizations USING btree (administered_date);

CREATE INDEX idx_immunizations_patient_id ON public.immunizations USING btree (patient_id);

CREATE INDEX idx_immunizations_vaccine_code ON public.immunizations USING btree (vaccine_code);

CREATE INDEX idx_immunizations_vaccine_name ON public.immunizations USING btree (vaccine_name);

CREATE INDEX idx_inventory_items_facility_id ON public.inventory_items USING btree (facility_id);

CREATE INDEX idx_inventory_items_organization_id ON public.inventory_items USING btree (organization_id);

CREATE INDEX idx_inventory_items_type ON public.inventory_items USING btree (type);

CREATE INDEX idx_inventory_transactions_item_id ON public.inventory_transactions USING btree (item_id);

CREATE INDEX idx_inventory_transactions_performed_by ON public.inventory_transactions USING btree (performed_by);

CREATE INDEX idx_lab_results_patient_date ON public.lab_results USING btree (patient_id, test_date);

CREATE INDEX idx_lab_results_provider_id ON public.lab_results USING btree (provider_id);

CREATE INDEX idx_medical_records_department_id ON public.medical_records USING btree (department_id);

CREATE INDEX idx_medical_records_org_dept ON public.medical_records USING btree (organization_id, department_id);

CREATE INDEX idx_medical_records_organization_id ON public.medical_records USING btree (organization_id);

CREATE INDEX idx_medical_records_patient_date ON public.medical_records USING btree (patient_id, visit_date);

CREATE INDEX idx_medical_records_provider_patient_date ON public.medical_records USING btree (provider_id, patient_id, visit_date);

CREATE INDEX idx_medical_records_provider_visit ON public.medical_records USING btree (provider_id, visit_date DESC);

CREATE INDEX idx_medical_records_visit_date_brin ON public.medical_records USING brin (visit_date) WITH (pages_per_range='32');

CREATE INDEX idx_medications_patient_active ON public.medications USING btree (patient_id, active);

CREATE INDEX idx_medications_provider_patient_active ON public.medications USING btree (provider_id, patient_id, active);

CREATE INDEX idx_message_states_user_id ON public.message_states USING btree (user_id);

CREATE INDEX idx_messages_conversation_id ON public.messages USING btree (conversation_id);

CREATE INDEX idx_messages_sender_id ON public.messages USING btree (sender_id);

CREATE INDEX idx_notifications_organization_id ON public.notifications USING btree (organization_id);

CREATE INDEX idx_notifications_recipient_id ON public.notifications USING btree (recipient_id);

CREATE INDEX idx_notifications_sender_id ON public.notifications USING btree (sender_id);

CREATE INDEX idx_orders_order_type ON public.orders USING btree (order_type);

CREATE INDEX idx_orders_ordered_at ON public.orders USING btree (ordered_at);

CREATE INDEX idx_orders_ordering_provider_id ON public.orders USING btree (ordering_provider_id);

CREATE INDEX idx_orders_patient_id ON public.orders USING btree (patient_id);

CREATE INDEX idx_orders_pending_priority ON public.orders USING btree (patient_id, priority) WHERE (status = 'pending'::order_status);

CREATE INDEX idx_orders_priority ON public.orders USING btree (priority);

CREATE INDEX idx_orders_scheduled_date ON public.orders USING btree (scheduled_date);

CREATE INDEX idx_orders_status_priority ON public.orders USING btree (status, priority) WHERE (status = 'pending'::order_status);

CREATE INDEX idx_org_invites_email ON public.organization_invites USING btree (email);

CREATE INDEX idx_org_invites_org_id ON public.organization_invites USING btree (organization_id);

CREATE INDEX idx_patient_alerts_alert_type ON public.patient_alerts USING btree (alert_type);

CREATE INDEX idx_patient_alerts_created_by ON public.patient_alerts USING btree (created_by);

CREATE INDEX idx_patient_alerts_patient_id ON public.patient_alerts USING btree (patient_id);

CREATE INDEX idx_patient_alerts_severity ON public.patient_alerts USING btree (severity);

CREATE INDEX idx_patient_alerts_status ON public.patient_alerts USING btree (status);

CREATE INDEX idx_patient_education_records_material_id ON public.patient_education_records USING btree (material_id);

CREATE INDEX idx_patient_education_records_patient_id ON public.patient_education_records USING btree (patient_id);

CREATE INDEX idx_patient_education_records_provided_date ON public.patient_education_records USING btree (provided_date);

CREATE INDEX idx_patient_education_records_provider_id ON public.patient_education_records USING btree (provider_id);

CREATE INDEX idx_patient_portal_settings_patient_id ON public.patient_portal_settings USING btree (patient_id);

CREATE INDEX idx_patient_questionnaires_patient_id ON public.patient_questionnaires USING btree (patient_id);

CREATE INDEX idx_patient_questionnaires_type ON public.patient_questionnaires USING btree (questionnaire_type);

CREATE INDEX idx_patients_organization_id ON public.patients USING btree (organization_id);

CREATE INDEX idx_patients_search_trigram ON public.patients USING gin ((((first_name || ' '::text) || last_name)) gin_trgm_ops);

CREATE INDEX idx_patients_user_id ON public.patients USING btree (user_id);

CREATE INDEX idx_referrals_patient_id ON public.referrals USING btree (patient_id);

CREATE INDEX idx_referrals_priority ON public.referrals USING btree (priority);

CREATE INDEX idx_referrals_referred_to_provider_id ON public.referrals USING btree (referred_to_provider_id);

CREATE INDEX idx_referrals_referring_provider_id ON public.referrals USING btree (referring_provider_id);

CREATE INDEX idx_referrals_status ON public.referrals USING btree (status);

CREATE INDEX idx_task_comments_task_id ON public.task_comments USING btree (task_id);

CREATE INDEX idx_task_comments_user_id ON public.task_comments USING btree (user_id);

CREATE INDEX idx_task_watchers_user_id ON public.task_watchers USING btree (user_id);

CREATE INDEX idx_tasks_assigned_by ON public.tasks USING btree (assigned_by);

CREATE INDEX idx_tasks_assigned_to ON public.tasks USING btree (assigned_to);

CREATE INDEX idx_tasks_department_id ON public.tasks USING btree (department_id);

CREATE INDEX idx_tasks_due_date ON public.tasks USING btree (due_date);

CREATE INDEX idx_tasks_organization_id ON public.tasks USING btree (organization_id);

CREATE INDEX idx_tasks_priority ON public.tasks USING btree (priority);

CREATE INDEX idx_tasks_status ON public.tasks USING btree (status);

CREATE INDEX idx_teams_department_id ON public.teams USING btree (department_id);

CREATE INDEX idx_teams_leader_id ON public.teams USING btree (leader_id);

CREATE INDEX idx_templates_organization_id ON public.templates USING btree (organization_id);

CREATE INDEX idx_templates_type ON public.templates USING btree (type);

CREATE INDEX idx_user_roles_department_id ON public.user_roles USING btree (department_id);

CREATE INDEX idx_user_roles_organization_id ON public.user_roles USING btree (organization_id);

CREATE INDEX idx_user_roles_user_id ON public.user_roles USING btree (user_id);

CREATE INDEX idx_user_roles_user_org ON public.user_roles USING btree (user_id, organization_id);

CREATE INDEX idx_vital_signs_patient_id ON public.vital_signs USING btree (patient_id);

CREATE INDEX idx_vital_signs_recorded_at_brin ON public.vital_signs USING brin (recorded_at);

CREATE INDEX idx_vital_signs_recorded_by ON public.vital_signs USING btree (recorded_by);

CREATE INDEX idx_workflow_instances_workflow_id ON public.workflow_instances USING btree (workflow_id);

CREATE INDEX idx_workflow_logs_instance_id ON public.workflow_logs USING btree (workflow_instance_id);

CREATE INDEX idx_workflows_created_by ON public.workflows USING btree (created_by);

CREATE INDEX idx_workflows_organization_id ON public.workflows USING btree (organization_id);

CREATE UNIQUE INDEX immunizations_pkey ON public.immunizations USING btree (id);

CREATE UNIQUE INDEX insurance_providers_pkey ON public.insurance_providers USING btree (id);

CREATE UNIQUE INDEX inventory_items_pkey ON public.inventory_items USING btree (id);

CREATE UNIQUE INDEX inventory_transactions_pkey ON public.inventory_transactions USING btree (id);

CREATE UNIQUE INDEX lab_results_pkey ON public.lab_results USING btree (id);

CREATE UNIQUE INDEX medical_records_pkey ON public.medical_records USING btree (id);

CREATE INDEX medical_records_search_idx ON public.medical_records USING gin (search_vector);

CREATE UNIQUE INDEX medications_pkey ON public.medications USING btree (id);

CREATE UNIQUE INDEX message_states_message_id_user_id_key ON public.message_states USING btree (message_id, user_id);

CREATE UNIQUE INDEX message_states_pkey ON public.message_states USING btree (id);

CREATE UNIQUE INDEX messages_pkey ON public.messages USING btree (id);

CREATE UNIQUE INDEX notification_preferences_pkey ON public.notification_preferences USING btree (id);

CREATE UNIQUE INDEX notification_preferences_user_id_type_key ON public.notification_preferences USING btree (user_id, type);

CREATE UNIQUE INDEX notification_templates_organization_id_name_key ON public.notification_templates USING btree (organization_id, name);

CREATE UNIQUE INDEX notification_templates_pkey ON public.notification_templates USING btree (id);

CREATE UNIQUE INDEX notifications_pkey ON public.notifications USING btree (id);

CREATE UNIQUE INDEX orders_pkey ON public.orders USING btree (id);

CREATE UNIQUE INDEX organization_invites_pkey ON public.organization_invites USING btree (id);

CREATE UNIQUE INDEX organizations_pkey ON public.organizations USING btree (id);

CREATE UNIQUE INDEX patient_alerts_pkey ON public.patient_alerts USING btree (id);

CREATE UNIQUE INDEX patient_education_records_pkey ON public.patient_education_records USING btree (id);

CREATE UNIQUE INDEX patient_portal_settings_pkey ON public.patient_portal_settings USING btree (id);

CREATE UNIQUE INDEX patient_questionnaires_pkey ON public.patient_questionnaires USING btree (id);

CREATE UNIQUE INDEX patients_pkey ON public.patients USING btree (id);

CREATE INDEX patients_search_idx ON public.patients USING gin (search_vector);

CREATE UNIQUE INDEX referrals_pkey ON public.referrals USING btree (id);

CREATE UNIQUE INDEX role_definitions_pkey ON public.role_definitions USING btree (role);

CREATE UNIQUE INDEX role_permissions_pkey ON public.role_permissions USING btree (id);

CREATE UNIQUE INDEX role_permissions_role_resource_key ON public.role_permissions USING btree (role, resource);

CREATE UNIQUE INDEX task_comments_pkey ON public.task_comments USING btree (id);

CREATE UNIQUE INDEX task_watchers_pkey ON public.task_watchers USING btree (id);

CREATE UNIQUE INDEX task_watchers_task_id_user_id_key ON public.task_watchers USING btree (task_id, user_id);

CREATE UNIQUE INDEX tasks_pkey ON public.tasks USING btree (id);

CREATE UNIQUE INDEX teams_pkey ON public.teams USING btree (id);

CREATE UNIQUE INDEX templates_pkey ON public.templates USING btree (id);

CREATE UNIQUE INDEX user_roles_pkey ON public.user_roles USING btree (id);

CREATE UNIQUE INDEX user_roles_user_id_organization_id_role_key ON public.user_roles USING btree (user_id, organization_id, role);

CREATE UNIQUE INDEX vital_signs_pkey ON public.vital_signs USING btree (id);

CREATE UNIQUE INDEX workflow_instances_pkey ON public.workflow_instances USING btree (id);

CREATE UNIQUE INDEX workflow_logs_pkey ON public.workflow_logs USING btree (id);

CREATE UNIQUE INDEX workflows_pkey ON public.workflows USING btree (id);

alter table "public"."activity_logs" add constraint "activity_logs_pkey" PRIMARY KEY using index "activity_logs_pkey";

alter table "public"."allergies" add constraint "allergies_pkey" PRIMARY KEY using index "allergies_pkey";

alter table "public"."analytics_events" add constraint "analytics_events_pkey" PRIMARY KEY using index "analytics_events_pkey";

alter table "public"."analytics_metrics" add constraint "analytics_metrics_pkey" PRIMARY KEY using index "analytics_metrics_pkey";

alter table "public"."appointments" add constraint "appointments_pkey" PRIMARY KEY using index "appointments_pkey";

alter table "public"."audit_logs" add constraint "audit_logs_pkey" PRIMARY KEY using index "audit_logs_pkey";

alter table "public"."billing_codes" add constraint "billing_codes_pkey" PRIMARY KEY using index "billing_codes_pkey";

alter table "public"."care_team_members" add constraint "care_team_members_pkey" PRIMARY KEY using index "care_team_members_pkey";

alter table "public"."claims" add constraint "claims_pkey" PRIMARY KEY using index "claims_pkey";

alter table "public"."clinical_notes" add constraint "clinical_notes_pkey" PRIMARY KEY using index "clinical_notes_pkey";

alter table "public"."conversation_participants" add constraint "conversation_participants_pkey" PRIMARY KEY using index "conversation_participants_pkey";

alter table "public"."conversations" add constraint "conversations_pkey" PRIMARY KEY using index "conversations_pkey";

alter table "public"."departments" add constraint "departments_pkey" PRIMARY KEY using index "departments_pkey";

alter table "public"."documents" add constraint "documents_pkey" PRIMARY KEY using index "documents_pkey";

alter table "public"."education_materials" add constraint "education_materials_pkey" PRIMARY KEY using index "education_materials_pkey";

alter table "public"."facilities" add constraint "facilities_pkey" PRIMARY KEY using index "facilities_pkey";

alter table "public"."healthcare_providers" add constraint "healthcare_providers_pkey" PRIMARY KEY using index "healthcare_providers_pkey";

alter table "public"."immunizations" add constraint "immunizations_pkey" PRIMARY KEY using index "immunizations_pkey";

alter table "public"."insurance_providers" add constraint "insurance_providers_pkey" PRIMARY KEY using index "insurance_providers_pkey";

alter table "public"."inventory_items" add constraint "inventory_items_pkey" PRIMARY KEY using index "inventory_items_pkey";

alter table "public"."inventory_transactions" add constraint "inventory_transactions_pkey" PRIMARY KEY using index "inventory_transactions_pkey";

alter table "public"."lab_results" add constraint "lab_results_pkey" PRIMARY KEY using index "lab_results_pkey";

alter table "public"."medical_records" add constraint "medical_records_pkey" PRIMARY KEY using index "medical_records_pkey";

alter table "public"."medications" add constraint "medications_pkey" PRIMARY KEY using index "medications_pkey";

alter table "public"."message_states" add constraint "message_states_pkey" PRIMARY KEY using index "message_states_pkey";

alter table "public"."messages" add constraint "messages_pkey" PRIMARY KEY using index "messages_pkey";

alter table "public"."notification_preferences" add constraint "notification_preferences_pkey" PRIMARY KEY using index "notification_preferences_pkey";

alter table "public"."notification_templates" add constraint "notification_templates_pkey" PRIMARY KEY using index "notification_templates_pkey";

alter table "public"."notifications" add constraint "notifications_pkey" PRIMARY KEY using index "notifications_pkey";

alter table "public"."orders" add constraint "orders_pkey" PRIMARY KEY using index "orders_pkey";

alter table "public"."organization_invites" add constraint "organization_invites_pkey" PRIMARY KEY using index "organization_invites_pkey";

alter table "public"."organizations" add constraint "organizations_pkey" PRIMARY KEY using index "organizations_pkey";

alter table "public"."patient_alerts" add constraint "patient_alerts_pkey" PRIMARY KEY using index "patient_alerts_pkey";

alter table "public"."patient_education_records" add constraint "patient_education_records_pkey" PRIMARY KEY using index "patient_education_records_pkey";

alter table "public"."patient_portal_settings" add constraint "patient_portal_settings_pkey" PRIMARY KEY using index "patient_portal_settings_pkey";

alter table "public"."patient_questionnaires" add constraint "patient_questionnaires_pkey" PRIMARY KEY using index "patient_questionnaires_pkey";

alter table "public"."patients" add constraint "patients_pkey" PRIMARY KEY using index "patients_pkey";

alter table "public"."referrals" add constraint "referrals_pkey" PRIMARY KEY using index "referrals_pkey";

alter table "public"."role_definitions" add constraint "role_definitions_pkey" PRIMARY KEY using index "role_definitions_pkey";

alter table "public"."role_permissions" add constraint "role_permissions_pkey" PRIMARY KEY using index "role_permissions_pkey";

alter table "public"."task_comments" add constraint "task_comments_pkey" PRIMARY KEY using index "task_comments_pkey";

alter table "public"."task_watchers" add constraint "task_watchers_pkey" PRIMARY KEY using index "task_watchers_pkey";

alter table "public"."tasks" add constraint "tasks_pkey" PRIMARY KEY using index "tasks_pkey";

alter table "public"."teams" add constraint "teams_pkey" PRIMARY KEY using index "teams_pkey";

alter table "public"."templates" add constraint "templates_pkey" PRIMARY KEY using index "templates_pkey";

alter table "public"."user_roles" add constraint "user_roles_pkey" PRIMARY KEY using index "user_roles_pkey";

alter table "public"."vital_signs" add constraint "vital_signs_pkey" PRIMARY KEY using index "vital_signs_pkey";

alter table "public"."workflow_instances" add constraint "workflow_instances_pkey" PRIMARY KEY using index "workflow_instances_pkey";

alter table "public"."workflow_logs" add constraint "workflow_logs_pkey" PRIMARY KEY using index "workflow_logs_pkey";

alter table "public"."workflows" add constraint "workflows_pkey" PRIMARY KEY using index "workflows_pkey";

alter table "public"."activity_logs" add constraint "activity_logs_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."activity_logs" validate constraint "activity_logs_organization_id_fkey";

alter table "public"."allergies" add constraint "allergies_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."allergies" validate constraint "allergies_patient_id_fkey";

alter table "public"."allergies" add constraint "allergies_reported_by_fkey" FOREIGN KEY (reported_by) REFERENCES auth.users(id) not valid;

alter table "public"."allergies" validate constraint "allergies_reported_by_fkey";

alter table "public"."analytics_events" add constraint "analytics_events_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."analytics_events" validate constraint "analytics_events_organization_id_fkey";

alter table "public"."analytics_events" add constraint "analytics_events_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."analytics_events" validate constraint "analytics_events_user_id_fkey";

alter table "public"."analytics_metrics" add constraint "analytics_metrics_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."analytics_metrics" validate constraint "analytics_metrics_organization_id_fkey";

alter table "public"."appointments" add constraint "appointments_department_id_fkey" FOREIGN KEY (department_id) REFERENCES departments(id) not valid;

alter table "public"."appointments" validate constraint "appointments_department_id_fkey";

alter table "public"."appointments" add constraint "appointments_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."appointments" validate constraint "appointments_organization_id_fkey";

alter table "public"."appointments" add constraint "appointments_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE not valid;

alter table "public"."appointments" validate constraint "appointments_patient_id_fkey";

alter table "public"."appointments" add constraint "appointments_provider_id_fkey" FOREIGN KEY (provider_id) REFERENCES healthcare_providers(id) ON DELETE CASCADE not valid;

alter table "public"."appointments" validate constraint "appointments_provider_id_fkey";

alter table "public"."audit_logs" add constraint "audit_logs_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES auth.users(id) not valid;

alter table "public"."audit_logs" validate constraint "audit_logs_changed_by_fkey";

alter table "public"."care_team_members" add constraint "care_team_members_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."care_team_members" validate constraint "care_team_members_patient_id_fkey";

alter table "public"."care_team_members" add constraint "care_team_members_provider_id_fkey" FOREIGN KEY (provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."care_team_members" validate constraint "care_team_members_provider_id_fkey";

alter table "public"."care_team_members" add constraint "valid_date_range" CHECK (((end_date IS NULL) OR (end_date >= start_date))) not valid;

alter table "public"."care_team_members" validate constraint "valid_date_range";

alter table "public"."claims" add constraint "claims_insurance_provider_id_fkey" FOREIGN KEY (insurance_provider_id) REFERENCES insurance_providers(id) not valid;

alter table "public"."claims" validate constraint "claims_insurance_provider_id_fkey";

alter table "public"."claims" add constraint "claims_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."claims" validate constraint "claims_patient_id_fkey";

alter table "public"."claims" add constraint "claims_provider_id_fkey" FOREIGN KEY (provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."claims" validate constraint "claims_provider_id_fkey";

alter table "public"."clinical_notes" add constraint "clinical_notes_medical_record_id_fkey" FOREIGN KEY (medical_record_id) REFERENCES medical_records(id) not valid;

alter table "public"."clinical_notes" validate constraint "clinical_notes_medical_record_id_fkey";

alter table "public"."clinical_notes" add constraint "clinical_notes_signed_by_fkey" FOREIGN KEY (signed_by) REFERENCES auth.users(id) not valid;

alter table "public"."clinical_notes" validate constraint "clinical_notes_signed_by_fkey";

alter table "public"."conversation_participants" add constraint "conversation_participants_conversation_id_fkey" FOREIGN KEY (conversation_id) REFERENCES conversations(id) not valid;

alter table "public"."conversation_participants" validate constraint "conversation_participants_conversation_id_fkey";

alter table "public"."conversation_participants" add constraint "conversation_participants_conversation_id_user_id_key" UNIQUE using index "conversation_participants_conversation_id_user_id_key";

alter table "public"."conversation_participants" add constraint "conversation_participants_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."conversation_participants" validate constraint "conversation_participants_user_id_fkey";

alter table "public"."conversations" add constraint "conversations_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) not valid;

alter table "public"."conversations" validate constraint "conversations_created_by_fkey";

alter table "public"."conversations" add constraint "conversations_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."conversations" validate constraint "conversations_organization_id_fkey";

alter table "public"."departments" add constraint "departments_facility_id_fkey" FOREIGN KEY (facility_id) REFERENCES facilities(id) not valid;

alter table "public"."departments" validate constraint "departments_facility_id_fkey";

alter table "public"."documents" add constraint "documents_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) not valid;

alter table "public"."documents" validate constraint "documents_created_by_fkey";

alter table "public"."documents" add constraint "documents_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."documents" validate constraint "documents_organization_id_fkey";

alter table "public"."documents" add constraint "documents_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."documents" validate constraint "documents_patient_id_fkey";

alter table "public"."facilities" add constraint "facilities_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."facilities" validate constraint "facilities_organization_id_fkey";

alter table "public"."healthcare_providers" add constraint "healthcare_providers_department_id_fkey" FOREIGN KEY (department_id) REFERENCES departments(id) not valid;

alter table "public"."healthcare_providers" validate constraint "healthcare_providers_department_id_fkey";

alter table "public"."healthcare_providers" add constraint "healthcare_providers_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."healthcare_providers" validate constraint "healthcare_providers_organization_id_fkey";

alter table "public"."healthcare_providers" add constraint "healthcare_providers_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."healthcare_providers" validate constraint "healthcare_providers_user_id_fkey";

alter table "public"."immunizations" add constraint "immunizations_administered_by_fkey" FOREIGN KEY (administered_by) REFERENCES healthcare_providers(id) not valid;

alter table "public"."immunizations" validate constraint "immunizations_administered_by_fkey";

alter table "public"."immunizations" add constraint "immunizations_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."immunizations" validate constraint "immunizations_patient_id_fkey";

alter table "public"."immunizations" add constraint "valid_dose_number" CHECK ((dose_number > 0)) not valid;

alter table "public"."immunizations" validate constraint "valid_dose_number";

alter table "public"."immunizations" add constraint "valid_expiration" CHECK ((expiration_date >= administered_date)) not valid;

alter table "public"."immunizations" validate constraint "valid_expiration";

alter table "public"."inventory_items" add constraint "inventory_items_facility_id_fkey" FOREIGN KEY (facility_id) REFERENCES facilities(id) not valid;

alter table "public"."inventory_items" validate constraint "inventory_items_facility_id_fkey";

alter table "public"."inventory_items" add constraint "inventory_items_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."inventory_items" validate constraint "inventory_items_organization_id_fkey";

alter table "public"."inventory_transactions" add constraint "inventory_transactions_item_id_fkey" FOREIGN KEY (item_id) REFERENCES inventory_items(id) not valid;

alter table "public"."inventory_transactions" validate constraint "inventory_transactions_item_id_fkey";

alter table "public"."inventory_transactions" add constraint "inventory_transactions_performed_by_fkey" FOREIGN KEY (performed_by) REFERENCES auth.users(id) not valid;

alter table "public"."inventory_transactions" validate constraint "inventory_transactions_performed_by_fkey";

alter table "public"."lab_results" add constraint "lab_results_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE not valid;

alter table "public"."lab_results" validate constraint "lab_results_patient_id_fkey";

alter table "public"."lab_results" add constraint "lab_results_provider_id_fkey" FOREIGN KEY (provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."lab_results" validate constraint "lab_results_provider_id_fkey";

alter table "public"."medical_records" add constraint "medical_records_department_id_fkey" FOREIGN KEY (department_id) REFERENCES departments(id) not valid;

alter table "public"."medical_records" validate constraint "medical_records_department_id_fkey";

alter table "public"."medical_records" add constraint "medical_records_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."medical_records" validate constraint "medical_records_organization_id_fkey";

alter table "public"."medical_records" add constraint "medical_records_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE not valid;

alter table "public"."medical_records" validate constraint "medical_records_patient_id_fkey";

alter table "public"."medical_records" add constraint "medical_records_provider_id_fkey" FOREIGN KEY (provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."medical_records" validate constraint "medical_records_provider_id_fkey";

alter table "public"."medications" add constraint "medications_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE not valid;

alter table "public"."medications" validate constraint "medications_patient_id_fkey";

alter table "public"."medications" add constraint "medications_provider_id_fkey" FOREIGN KEY (provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."medications" validate constraint "medications_provider_id_fkey";

alter table "public"."message_states" add constraint "message_states_message_id_fkey" FOREIGN KEY (message_id) REFERENCES messages(id) not valid;

alter table "public"."message_states" validate constraint "message_states_message_id_fkey";

alter table "public"."message_states" add constraint "message_states_message_id_user_id_key" UNIQUE using index "message_states_message_id_user_id_key";

alter table "public"."message_states" add constraint "message_states_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."message_states" validate constraint "message_states_user_id_fkey";

alter table "public"."messages" add constraint "messages_conversation_id_fkey" FOREIGN KEY (conversation_id) REFERENCES conversations(id) not valid;

alter table "public"."messages" validate constraint "messages_conversation_id_fkey";

alter table "public"."messages" add constraint "messages_sender_id_fkey" FOREIGN KEY (sender_id) REFERENCES auth.users(id) not valid;

alter table "public"."messages" validate constraint "messages_sender_id_fkey";

alter table "public"."notification_preferences" add constraint "notification_preferences_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."notification_preferences" validate constraint "notification_preferences_user_id_fkey";

alter table "public"."notification_preferences" add constraint "notification_preferences_user_id_type_key" UNIQUE using index "notification_preferences_user_id_type_key";

alter table "public"."notification_templates" add constraint "notification_templates_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."notification_templates" validate constraint "notification_templates_organization_id_fkey";

alter table "public"."notification_templates" add constraint "notification_templates_organization_id_name_key" UNIQUE using index "notification_templates_organization_id_name_key";

alter table "public"."notifications" add constraint "notifications_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."notifications" validate constraint "notifications_organization_id_fkey";

alter table "public"."notifications" add constraint "notifications_recipient_id_fkey" FOREIGN KEY (recipient_id) REFERENCES auth.users(id) not valid;

alter table "public"."notifications" validate constraint "notifications_recipient_id_fkey";

alter table "public"."notifications" add constraint "notifications_sender_id_fkey" FOREIGN KEY (sender_id) REFERENCES auth.users(id) not valid;

alter table "public"."notifications" validate constraint "notifications_sender_id_fkey";

alter table "public"."orders" add constraint "orders_ordering_provider_id_fkey" FOREIGN KEY (ordering_provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."orders" validate constraint "orders_ordering_provider_id_fkey";

alter table "public"."orders" add constraint "orders_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."orders" validate constraint "orders_patient_id_fkey";

alter table "public"."orders" add constraint "valid_order_dates" CHECK ((((scheduled_date IS NULL) OR (scheduled_date >= ordered_at)) AND ((completed_at IS NULL) OR (completed_at >= ordered_at)))) not valid;

alter table "public"."orders" validate constraint "valid_order_dates";

alter table "public"."organization_invites" add constraint "organization_invites_invited_by_fkey" FOREIGN KEY (invited_by) REFERENCES auth.users(id) not valid;

alter table "public"."organization_invites" validate constraint "organization_invites_invited_by_fkey";

alter table "public"."organization_invites" add constraint "organization_invites_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE not valid;

alter table "public"."organization_invites" validate constraint "organization_invites_organization_id_fkey";

alter table "public"."organization_invites" add constraint "organization_invites_status_check" CHECK ((status = ANY (ARRAY['pending'::text, 'accepted'::text, 'declined'::text]))) not valid;

alter table "public"."organization_invites" validate constraint "organization_invites_status_check";

alter table "public"."organizations" add constraint "organizations_owner_id_fkey" FOREIGN KEY (owner_id) REFERENCES auth.users(id) not valid;

alter table "public"."organizations" validate constraint "organizations_owner_id_fkey";

alter table "public"."patient_alerts" add constraint "patient_alerts_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) not valid;

alter table "public"."patient_alerts" validate constraint "patient_alerts_created_by_fkey";

alter table "public"."patient_alerts" add constraint "patient_alerts_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."patient_alerts" validate constraint "patient_alerts_patient_id_fkey";

alter table "public"."patient_alerts" add constraint "valid_date_range" CHECK (((end_date IS NULL) OR (end_date >= start_date))) not valid;

alter table "public"."patient_alerts" validate constraint "valid_date_range";

alter table "public"."patient_education_records" add constraint "patient_education_records_material_id_fkey" FOREIGN KEY (material_id) REFERENCES education_materials(id) not valid;

alter table "public"."patient_education_records" validate constraint "patient_education_records_material_id_fkey";

alter table "public"."patient_education_records" add constraint "patient_education_records_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."patient_education_records" validate constraint "patient_education_records_patient_id_fkey";

alter table "public"."patient_education_records" add constraint "patient_education_records_provider_id_fkey" FOREIGN KEY (provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."patient_education_records" validate constraint "patient_education_records_provider_id_fkey";

alter table "public"."patient_portal_settings" add constraint "patient_portal_settings_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."patient_portal_settings" validate constraint "patient_portal_settings_patient_id_fkey";

alter table "public"."patient_questionnaires" add constraint "patient_questionnaires_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."patient_questionnaires" validate constraint "patient_questionnaires_patient_id_fkey";

alter table "public"."patients" add constraint "patients_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."patients" validate constraint "patients_organization_id_fkey";

alter table "public"."patients" add constraint "patients_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."patients" validate constraint "patients_user_id_fkey";

alter table "public"."referrals" add constraint "referrals_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."referrals" validate constraint "referrals_patient_id_fkey";

alter table "public"."referrals" add constraint "referrals_referred_to_provider_id_fkey" FOREIGN KEY (referred_to_provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."referrals" validate constraint "referrals_referred_to_provider_id_fkey";

alter table "public"."referrals" add constraint "referrals_referring_provider_id_fkey" FOREIGN KEY (referring_provider_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."referrals" validate constraint "referrals_referring_provider_id_fkey";

alter table "public"."referrals" add constraint "valid_referral_dates" CHECK ((((scheduled_date IS NULL) OR (scheduled_date >= referral_date)) AND ((completed_date IS NULL) OR (completed_date >= referral_date)))) not valid;

alter table "public"."referrals" validate constraint "valid_referral_dates";

alter table "public"."role_permissions" add constraint "fk_role_permissions_role" FOREIGN KEY (role) REFERENCES role_definitions(role) ON DELETE CASCADE not valid;

alter table "public"."role_permissions" validate constraint "fk_role_permissions_role";

alter table "public"."role_permissions" add constraint "role_permissions_role_resource_key" UNIQUE using index "role_permissions_role_resource_key";

alter table "public"."task_comments" add constraint "task_comments_task_id_fkey" FOREIGN KEY (task_id) REFERENCES tasks(id) not valid;

alter table "public"."task_comments" validate constraint "task_comments_task_id_fkey";

alter table "public"."task_comments" add constraint "task_comments_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."task_comments" validate constraint "task_comments_user_id_fkey";

alter table "public"."task_watchers" add constraint "task_watchers_task_id_fkey" FOREIGN KEY (task_id) REFERENCES tasks(id) not valid;

alter table "public"."task_watchers" validate constraint "task_watchers_task_id_fkey";

alter table "public"."task_watchers" add constraint "task_watchers_task_id_user_id_key" UNIQUE using index "task_watchers_task_id_user_id_key";

alter table "public"."task_watchers" add constraint "task_watchers_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."task_watchers" validate constraint "task_watchers_user_id_fkey";

alter table "public"."tasks" add constraint "tasks_assigned_by_fkey" FOREIGN KEY (assigned_by) REFERENCES auth.users(id) not valid;

alter table "public"."tasks" validate constraint "tasks_assigned_by_fkey";

alter table "public"."tasks" add constraint "tasks_assigned_to_fkey" FOREIGN KEY (assigned_to) REFERENCES auth.users(id) not valid;

alter table "public"."tasks" validate constraint "tasks_assigned_to_fkey";

alter table "public"."tasks" add constraint "tasks_department_id_fkey" FOREIGN KEY (department_id) REFERENCES departments(id) not valid;

alter table "public"."tasks" validate constraint "tasks_department_id_fkey";

alter table "public"."tasks" add constraint "tasks_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."tasks" validate constraint "tasks_organization_id_fkey";

alter table "public"."teams" add constraint "teams_department_id_fkey" FOREIGN KEY (department_id) REFERENCES departments(id) not valid;

alter table "public"."teams" validate constraint "teams_department_id_fkey";

alter table "public"."teams" add constraint "teams_leader_id_fkey" FOREIGN KEY (leader_id) REFERENCES healthcare_providers(id) not valid;

alter table "public"."teams" validate constraint "teams_leader_id_fkey";

alter table "public"."templates" add constraint "templates_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."templates" validate constraint "templates_organization_id_fkey";

alter table "public"."user_roles" add constraint "fk_user_roles_role" FOREIGN KEY (role) REFERENCES role_definitions(role) ON DELETE CASCADE not valid;

alter table "public"."user_roles" validate constraint "fk_user_roles_role";

alter table "public"."user_roles" add constraint "user_roles_department_id_fkey" FOREIGN KEY (department_id) REFERENCES departments(id) not valid;

alter table "public"."user_roles" validate constraint "user_roles_department_id_fkey";

alter table "public"."user_roles" add constraint "user_roles_invitation_status_check" CHECK ((invitation_status = ANY (ARRAY['direct'::text, 'invited'::text, 'accepted'::text]))) not valid;

alter table "public"."user_roles" validate constraint "user_roles_invitation_status_check";

alter table "public"."user_roles" add constraint "user_roles_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."user_roles" validate constraint "user_roles_organization_id_fkey";

alter table "public"."user_roles" add constraint "user_roles_user_id_organization_id_role_key" UNIQUE using index "user_roles_user_id_organization_id_role_key";

alter table "public"."vital_signs" add constraint "valid_blood_pressure_diastolic" CHECK (((blood_pressure_diastolic > 0) AND (blood_pressure_diastolic < 200))) not valid;

alter table "public"."vital_signs" validate constraint "valid_blood_pressure_diastolic";

alter table "public"."vital_signs" add constraint "valid_blood_pressure_systolic" CHECK (((blood_pressure_systolic > 0) AND (blood_pressure_systolic < 300))) not valid;

alter table "public"."vital_signs" validate constraint "valid_blood_pressure_systolic";

alter table "public"."vital_signs" add constraint "valid_heart_rate" CHECK (((heart_rate > 0) AND (heart_rate < 300))) not valid;

alter table "public"."vital_signs" validate constraint "valid_heart_rate";

alter table "public"."vital_signs" add constraint "valid_oxygen_saturation" CHECK (((oxygen_saturation >= 0) AND (oxygen_saturation <= 100))) not valid;

alter table "public"."vital_signs" validate constraint "valid_oxygen_saturation";

alter table "public"."vital_signs" add constraint "valid_pain_level" CHECK (((pain_level >= 0) AND (pain_level <= 10))) not valid;

alter table "public"."vital_signs" validate constraint "valid_pain_level";

alter table "public"."vital_signs" add constraint "valid_respiratory_rate" CHECK (((respiratory_rate > 0) AND (respiratory_rate < 100))) not valid;

alter table "public"."vital_signs" validate constraint "valid_respiratory_rate";

alter table "public"."vital_signs" add constraint "vital_signs_patient_id_fkey" FOREIGN KEY (patient_id) REFERENCES patients(id) not valid;

alter table "public"."vital_signs" validate constraint "vital_signs_patient_id_fkey";

alter table "public"."vital_signs" add constraint "vital_signs_recorded_by_fkey" FOREIGN KEY (recorded_by) REFERENCES auth.users(id) not valid;

alter table "public"."vital_signs" validate constraint "vital_signs_recorded_by_fkey";

alter table "public"."workflow_instances" add constraint "workflow_instances_workflow_id_fkey" FOREIGN KEY (workflow_id) REFERENCES workflows(id) not valid;

alter table "public"."workflow_instances" validate constraint "workflow_instances_workflow_id_fkey";

alter table "public"."workflow_logs" add constraint "workflow_logs_workflow_instance_id_fkey" FOREIGN KEY (workflow_instance_id) REFERENCES workflow_instances(id) not valid;

alter table "public"."workflow_logs" validate constraint "workflow_logs_workflow_instance_id_fkey";

alter table "public"."workflows" add constraint "workflows_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) not valid;

alter table "public"."workflows" validate constraint "workflows_created_by_fkey";

alter table "public"."workflows" add constraint "workflows_organization_id_fkey" FOREIGN KEY (organization_id) REFERENCES organizations(id) not valid;

alter table "public"."workflows" validate constraint "workflows_organization_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.accept_organization_invite(invite_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
  v_org_id UUID;
  v_role TEXT;
  v_email TEXT;
BEGIN
  -- Get and validate invite
  SELECT organization_id, role, email
  INTO v_org_id, v_role, v_email
  FROM public.organization_invites
  WHERE id = invite_id
    AND status = 'pending'
    AND expires_at > NOW()
    AND email = auth.jwt()->>'email';

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Begin transaction
  BEGIN
    -- Update invite status
    UPDATE public.organization_invites
    SET status = 'accepted'
    WHERE id = invite_id;

    -- Create user role
    INSERT INTO public.user_roles (user_id, organization_id, role, invitation_status)
    VALUES (auth.uid(), v_org_id, v_role, 'accepted');

    RETURN TRUE;
  EXCEPTION WHEN OTHERS THEN
    RETURN FALSE;
  END;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.add_lab_results(p_patient_id uuid, p_provider_id uuid, p_test_name text, p_results jsonb, p_normal_range jsonb, p_notes text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_lab_result_id UUID;
BEGIN
    INSERT INTO lab_results (
        patient_id,
        provider_id,
        test_name,
        test_date,
        results,
        normal_range,
        notes
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_test_name,
        CURRENT_TIMESTAMP,
        p_results,
        p_normal_range,
        p_notes
    ) RETURNING id INTO v_lab_result_id;

    RETURN v_lab_result_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.add_medical_record(p_patient_id uuid, p_provider_id uuid, p_chief_complaint text, p_diagnosis text[], p_treatment_plan text, p_notes text, p_attachments jsonb DEFAULT '{"files": []}'::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_record_id UUID;
BEGIN
    INSERT INTO medical_records (
        patient_id,
        provider_id,
        visit_date,
        chief_complaint,
        diagnosis,
        treatment_plan,
        notes,
        attachments
    ) VALUES (
        p_patient_id,
        p_provider_id,
        CURRENT_TIMESTAMP,
        p_chief_complaint,
        p_diagnosis,
        p_treatment_plan,
        p_notes,
        p_attachments
    ) RETURNING id INTO v_record_id;

    RETURN v_record_id;
END;
$function$
;

create materialized view "public"."analytics_daily_appointments" as  SELECT appointments.organization_id,
    date_trunc('day'::text, appointments.appointment_date) AS date,
    appointments.department_id,
    count(*) AS total_appointments,
    count(*) FILTER (WHERE (appointments.status = 'completed'::appointment_status)) AS completed_appointments,
    count(*) FILTER (WHERE (appointments.status = 'cancelled'::appointment_status)) AS cancelled_appointments,
    avg(appointments.duration_minutes) AS avg_duration
   FROM appointments
  GROUP BY appointments.organization_id, (date_trunc('day'::text, appointments.appointment_date)), appointments.department_id;


create materialized view "public"."analytics_patient_metrics" as  SELECT p.organization_id,
    count(DISTINCT p.id) AS total_patients,
    count(DISTINCT
        CASE
            WHEN (a.id IS NOT NULL) THEN p.id
            ELSE NULL::uuid
        END) AS active_patients,
    avg(EXTRACT(year FROM age((p.date_of_birth)::timestamp with time zone))) AS avg_patient_age,
    count(DISTINCT a.id) AS total_appointments,
    count(DISTINCT mr.id) AS total_medical_records,
    count(DISTINCT m.id) AS total_medications
   FROM (((patients p
     LEFT JOIN appointments a ON ((a.patient_id = p.id)))
     LEFT JOIN medical_records mr ON ((mr.patient_id = p.id)))
     LEFT JOIN medications m ON ((m.patient_id = p.id)))
  GROUP BY p.organization_id;


create materialized view "public"."analytics_provider_metrics" as  SELECT hp.organization_id,
    hp.id AS provider_id,
    ((hp.first_name || ' '::text) || hp.last_name) AS provider_name,
    hp.role,
    count(DISTINCT a.id) AS total_appointments,
    count(DISTINCT a.patient_id) AS unique_patients,
    count(DISTINCT mr.id) AS medical_records_created,
    count(DISTINCT m.id) AS medications_prescribed
   FROM (((healthcare_providers hp
     LEFT JOIN appointments a ON ((a.provider_id = hp.id)))
     LEFT JOIN medical_records mr ON ((mr.provider_id = hp.id)))
     LEFT JOIN medications m ON ((m.provider_id = hp.id)))
  GROUP BY hp.organization_id, hp.id, hp.first_name, hp.last_name, hp.role;


CREATE OR REPLACE FUNCTION public.assign_task(p_title text, p_description text, p_assigned_to uuid, p_priority task_priority DEFAULT 'medium'::task_priority, p_due_date timestamp with time zone DEFAULT NULL::timestamp with time zone, p_related_to jsonb DEFAULT NULL::jsonb, p_metadata jsonb DEFAULT NULL::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_task_id UUID;
    v_org_id UUID;
    v_dept_id UUID;
BEGIN
    -- Get organization and department ID from assigned user's role
    SELECT organization_id, department_id INTO v_org_id, v_dept_id
    FROM user_roles
    WHERE user_id = p_assigned_to
    LIMIT 1;

    -- Create task
    INSERT INTO tasks (
        organization_id,
        department_id,
        title,
        description,
        priority,
        assigned_to,
        assigned_by,
        due_date,
        related_to,
        metadata
    ) VALUES (
        v_org_id,
        v_dept_id,
        p_title,
        p_description,
        p_priority,
        p_assigned_to,
        auth.uid(),
        p_due_date,
        COALESCE(p_related_to, '{}'::jsonb),
        COALESCE(p_metadata, '{}'::jsonb)
    ) RETURNING id INTO v_task_id;

    -- Send notification to assigned user
    PERFORM send_notification(
        'task_assignment'::notification_type,
        p_assigned_to,
        'New Task Assigned: ' || p_title,
        p_description,
        jsonb_build_object('task_id', v_task_id)
    );

    RETURN v_task_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.audit_log_changes()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    old_data jsonb;
    new_data jsonb;
BEGIN
    -- Only proceed if the audit_logs table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'audit_logs') THEN
        IF (TG_OP = 'DELETE') THEN
            old_data = to_jsonb(OLD);
            new_data = null;
        ELSIF (TG_OP = 'UPDATE') THEN
            old_data = to_jsonb(OLD);
            new_data = to_jsonb(NEW);
        ELSIF (TG_OP = 'INSERT') THEN
            old_data = null;
            new_data = to_jsonb(NEW);
        END IF;

        -- Insert audit log
        INSERT INTO public.audit_logs (
            table_name,
            record_id,
            action,
            old_data,
            new_data,
            changed_by,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            CASE
                WHEN TG_OP = 'DELETE' THEN OLD.id
                ELSE NEW.id
            END,
            TG_OP,
            old_data,
            new_data,
            auth.uid(),
            CURRENT_TIMESTAMP
        );
    END IF;

    RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.can_access_analytics(org_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = (select auth.uid()) 
    AND ur.organization_id = org_id
    AND ur.role::text IN ('super_admin', 'admin', 'staff') 
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_user_permission(p_user_id uuid, p_resource text, p_action text, p_context jsonb DEFAULT '{}'::jsonb)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_role user_role;
    v_permissions JSONB;
BEGIN
    -- Get user's role and custom permissions
    SELECT role, custom_permissions
    INTO v_role, v_permissions
    FROM user_roles
    WHERE user_id = p_user_id;

    -- Check if user has permission through role
    RETURN EXISTS (
        SELECT 1
        FROM role_permissions
        WHERE role = v_role
        AND resource = p_resource
        AND p_action = ANY(actions)
        AND (
            conditions @> p_context
            OR
            custom_permissions->p_resource->p_action = 'true'
        )
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.clean_expired_invites()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    DELETE FROM public.organization_invites
    WHERE expires_at < now()
    AND status = 'pending';
END;
$function$
;

CREATE OR REPLACE FUNCTION public.decline_organization_invite(invite_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  UPDATE public.organization_invites
  SET status = 'declined'
  WHERE id = invite_id
    AND status = 'pending'
    AND expires_at > NOW()
    AND email = auth.jwt()->>'email';

  RETURN FOUND;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.disable_rls()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    -- This function is used for testing purposes only
    -- It should not be used in production
    RAISE NOTICE 'RLS has been temporarily disabled for testing';
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_appointment_statistics(p_provider_id uuid DEFAULT NULL::uuid, p_start_date date DEFAULT (CURRENT_DATE - '30 days'::interval), p_end_date date DEFAULT CURRENT_DATE)
 RETURNS TABLE(total_appointments integer, completed_appointments integer, cancelled_appointments integer, avg_duration numeric, most_common_reason text)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    WITH appointment_stats AS (
        SELECT 
            COUNT(*) as total,
            COUNT(*) FILTER (WHERE status = 'completed') as completed,
            COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
            AVG(duration_minutes) as avg_dur,
            MODE() WITHIN GROUP (ORDER BY reason) as common_reason
        FROM appointments
        WHERE 
            appointment_date::DATE BETWEEN p_start_date AND p_end_date
            AND (p_provider_id IS NULL OR provider_id = p_provider_id)
    )
    SELECT 
        total,
        completed,
        cancelled,
        ROUND(avg_dur::DECIMAL, 2),
        common_reason
    FROM appointment_stats;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_medication_statistics(p_provider_id uuid DEFAULT NULL::uuid, p_start_date date DEFAULT (CURRENT_DATE - '30 days'::interval))
 RETURNS TABLE(medication_name text, total_prescriptions integer, active_prescriptions integer, avg_duration_days integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        m.medication_name,
        COUNT(*)::INTEGER as total_prescriptions,
        COUNT(*) FILTER (WHERE m.active = true)::INTEGER as active_prescriptions,
        AVG(
            CASE 
                WHEN m.end_date IS NOT NULL THEN 
                    (m.end_date - m.start_date)
                ELSE
                    (CURRENT_DATE - m.start_date)
            END
        )::INTEGER as avg_duration_days
    FROM medications m
    WHERE 
        m.start_date >= p_start_date
        AND (p_provider_id IS NULL OR m.provider_id = p_provider_id)
    GROUP BY m.medication_name
    ORDER BY total_prescriptions DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_organization_metrics(p_organization_id uuid, p_start_date timestamp with time zone DEFAULT (CURRENT_DATE - '30 days'::interval), p_end_date timestamp with time zone DEFAULT CURRENT_DATE)
 RETURNS TABLE(metric_name text, current_value numeric, previous_value numeric, change_percentage numeric)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    WITH current_period AS (
        SELECT 
            'appointments' as metric_name,
            COUNT(*) as value
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'patients',
            COUNT(DISTINCT patient_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'providers',
            COUNT(DISTINCT provider_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'avg_duration',
            AVG(duration_minutes)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
    ),
    previous_period AS (
        SELECT 
            'appointments' as metric_name,
            COUNT(*) as value
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'patients',
            COUNT(DISTINCT patient_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'providers',
            COUNT(DISTINCT provider_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'avg_duration',
            AVG(duration_minutes)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
    )
    SELECT 
        c.metric_name,
        c.value as current_value,
        p.value as previous_value,
        CASE 
            WHEN p.value = 0 THEN NULL
            ELSE ((c.value - p.value) / p.value * 100)
        END as change_percentage
    FROM current_period c
    LEFT JOIN previous_period p ON c.metric_name = p.metric_name;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_patient_demographics(p_provider_id uuid DEFAULT NULL::uuid)
 RETURNS TABLE(age_group text, gender_distribution jsonb, patient_count integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    WITH patient_ages AS (
        SELECT 
            CASE 
                WHEN age(date_of_birth) < INTERVAL '18 years' THEN '0-17'
                WHEN age(date_of_birth) < INTERVAL '30 years' THEN '18-29'
                WHEN age(date_of_birth) < INTERVAL '50 years' THEN '30-49'
                WHEN age(date_of_birth) < INTERVAL '70 years' THEN '50-69'
                ELSE '70+'
            END as age_group,
            gender
        FROM patients p
        WHERE p_provider_id IS NULL OR EXISTS (
            SELECT 1 FROM medical_records mr 
            WHERE mr.patient_id = p.id 
            AND mr.provider_id = p_provider_id
        )
    )
    SELECT 
        age_group,
        jsonb_object_agg(gender, COUNT(*)::INTEGER) as gender_distribution,
        COUNT(*)::INTEGER as patient_count
    FROM patient_ages
    GROUP BY age_group
    ORDER BY age_group;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_patient_summary(p_patient_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_summary jsonb;
BEGIN
    SELECT jsonb_build_object(
        'patient_id', p.id,
        'name', p.first_name || ' ' || p.last_name,
        'age', extract(year from age(now(), p.date_of_birth)),
        'gender', p.gender,
        'recent_visits', (
            SELECT jsonb_agg(jsonb_build_object(
                'date', a.appointment_date,
                'provider', pr.first_name || ' ' || pr.last_name,
                'reason', a.reason
            ))
            FROM appointments a
            JOIN providers pr ON a.provider_id = pr.id
            WHERE a.patient_id = p_patient_id
            ORDER BY a.appointment_date DESC
            LIMIT 5
        ),
        'active_medications', (
            SELECT jsonb_agg(jsonb_build_object(
                'name', m.medication_name,
                'dosage', m.dosage,
                'frequency', m.frequency
            ))
            FROM medications m
            WHERE m.patient_id = p_patient_id AND m.active = true
        )
    ) INTO v_summary
    FROM patients p
    WHERE p.id = p_patient_id;

    RETURN v_summary;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_patient_visit_history(p_patient_id uuid, p_start_date date DEFAULT (CURRENT_DATE - '1 year'::interval), p_end_date date DEFAULT CURRENT_DATE)
 RETURNS TABLE(visit_date date, visit_type text, provider_name text, diagnosis text[], medications_prescribed text[], lab_tests_performed text[])
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    WITH visit_data AS (
        SELECT 
            mr.visit_date::DATE,
            'Medical Record'::TEXT as visit_type,
            hp.first_name || ' ' || hp.last_name as provider_name,
            mr.diagnosis,
            ARRAY(
                SELECT m.medication_name
                FROM medications m
                WHERE m.patient_id = mr.patient_id
                AND m.start_date::DATE = mr.visit_date::DATE
            ) as medications,
            ARRAY(
                SELECT lr.test_name
                FROM lab_results lr
                WHERE lr.patient_id = mr.patient_id
                AND lr.test_date::DATE = mr.visit_date::DATE
            ) as lab_tests
        FROM medical_records mr
        JOIN healthcare_providers hp ON hp.id = mr.provider_id
        WHERE 
            mr.patient_id = p_patient_id
            AND mr.visit_date::DATE BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            a.appointment_date::DATE,
            'Appointment'::TEXT,
            hp.first_name || ' ' || hp.last_name,
            NULL::TEXT[],
            NULL::TEXT[],
            NULL::TEXT[]
        FROM appointments a
        JOIN healthcare_providers hp ON hp.id = a.provider_id
        WHERE 
            a.patient_id = p_patient_id
            AND a.appointment_date::DATE BETWEEN p_start_date AND p_end_date
            AND NOT EXISTS (
                SELECT 1 
                FROM medical_records mr 
                WHERE mr.patient_id = a.patient_id 
                AND mr.visit_date::DATE = a.appointment_date::DATE
            )
    )
    SELECT * FROM visit_data
    ORDER BY visit_date DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_provider_workload(p_start_date date DEFAULT (CURRENT_DATE - '30 days'::interval), p_end_date date DEFAULT CURRENT_DATE)
 RETURNS TABLE(provider_id uuid, provider_name text, provider_type provider_type, total_appointments integer, total_patients integer, avg_appointments_per_day numeric)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    WITH provider_stats AS (
        SELECT 
            hp.id,
            hp.first_name || ' ' || hp.last_name as name,
            hp.provider_type,
            COUNT(DISTINCT a.id) as appointment_count,
            COUNT(DISTINCT a.patient_id) as patient_count,
            COUNT(DISTINCT a.id)::DECIMAL / 
                GREATEST(1, (p_end_date - p_start_date + 1)) as avg_daily_appointments
        FROM healthcare_providers hp
        LEFT JOIN appointments a ON 
            a.provider_id = hp.id AND 
            a.appointment_date::DATE BETWEEN p_start_date AND p_end_date
        GROUP BY hp.id, hp.first_name, hp.last_name, hp.provider_type
    )
    SELECT 
        id,
        name,
        provider_type,
        appointment_count,
        patient_count,
        ROUND(avg_daily_appointments, 2)
    FROM provider_stats
    ORDER BY appointment_count DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_departments(p_user_id uuid)
 RETURNS TABLE(department_id uuid, department_name text)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    SELECT d.id, d.name
    FROM public.departments d
    JOIN public.department_members dm ON d.id = dm.department_id
    WHERE dm.user_id = p_user_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.has_patient_access(provider_id uuid, patient_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM medical_records
        WHERE provider_id = provider_id
        AND patient_id = patient_id
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_healthcare_provider(user_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM healthcare_providers
        WHERE id = user_id
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_patient(user_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM patients
        WHERE id = user_id
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_message_read(p_message_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    -- Update message status
    UPDATE message_states
    SET 
        state = 'read',
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        message_id = p_message_id
        AND user_id = auth.uid()
        AND state != 'read';

    -- Update last read timestamp in conversation
    UPDATE conversation_participants
    SET last_read_at = CURRENT_TIMESTAMP
    WHERE 
        conversation_id = (SELECT conversation_id FROM messages WHERE id = p_message_id)
        AND user_id = auth.uid();

    RETURN FOUND;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_notification_read(p_notification_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    UPDATE notifications
    SET 
        status = 'read'::notification_status,
        read_at = CURRENT_TIMESTAMP
    WHERE 
        id = p_notification_id
        AND recipient_id = auth.uid()
        AND status = 'delivered'::notification_status;
    
    RETURN FOUND;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.prescribe_medication(p_patient_id uuid, p_provider_id uuid, p_medication_name text, p_dosage text, p_frequency text, p_start_date date, p_end_date date, p_instructions text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_medication_id UUID;
BEGIN
    INSERT INTO medications (
        patient_id,
        provider_id,
        medication_name,
        dosage,
        frequency,
        start_date,
        end_date,
        instructions,
        active
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_medication_name,
        p_dosage,
        p_frequency,
        p_start_date,
        p_end_date,
        p_instructions,
        TRUE
    ) RETURNING id INTO v_medication_id;

    RETURN v_medication_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.random_date(start_date date, end_date date)
 RETURNS date
 LANGUAGE sql
 SET search_path TO ''
AS $function$
  SELECT (start_date + (random() * (end_date - start_date))::integer)::date;
$function$
;

CREATE OR REPLACE FUNCTION public.random_timestamp(start_timestamp timestamp without time zone, end_timestamp timestamp without time zone)
 RETURNS timestamp without time zone
 LANGUAGE sql
 SET search_path TO ''
AS $function$
  SELECT (start_timestamp + (random() * (extract(epoch from end_timestamp) - extract(epoch from start_timestamp))::integer * interval '1 second'))::timestamp;
$function$
;

CREATE OR REPLACE FUNCTION public.record_analytics_event(p_event_type text, p_event_data jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_org_id UUID;
    v_event_id UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM public.user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Record event
    INSERT INTO public.analytics_events (
        organization_id,
        event_type,
        event_data,
        user_id
    ) VALUES (
        v_org_id,
        p_event_type,
        p_event_data,
        auth.uid()
    ) RETURNING id INTO v_event_id;

    RETURN v_event_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.record_analytics_event(p_event_type text, p_user_id uuid, p_data jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_event_id UUID;
BEGIN
    INSERT INTO analytics (
        event_type,
        user_id,
        event_data,
        created_at
    ) VALUES (
        p_event_type,
        p_user_id,
        p_data,
        now()
    ) RETURNING id INTO v_event_id;

    RETURN v_event_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.record_analytics_metric(p_metric_name text, p_metric_value numeric, p_dimensions jsonb DEFAULT NULL::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_org_id UUID;
    v_metric_id UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Record metric
    INSERT INTO analytics_metrics (
        organization_id,
        metric_name,
        metric_value,
        dimensions
    ) VALUES (
        v_org_id,
        p_metric_name,
        p_metric_value,
        COALESCE(p_dimensions, '{}'::jsonb)
    ) RETURNING id INTO v_metric_id;

    RETURN v_metric_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_analytics_views()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    REFRESH MATERIALIZED VIEW public.analytics_patient_summary;
    REFRESH MATERIALIZED VIEW public.analytics_daily_summary;
    REFRESH MATERIALIZED VIEW public.analytics_summary;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_materialized_views()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.analytics_daily_appointments;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.analytics_patient_metrics;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.analytics_provider_metrics;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.schedule_appointment(p_patient_id uuid, p_provider_id uuid, p_appointment_date timestamp without time zone, p_duration_minutes integer, p_reason text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_appointment_id UUID;
    v_conflict_count INTEGER;
BEGIN
    -- Check for scheduling conflicts
    SELECT COUNT(*)
    INTO v_conflict_count
    FROM appointments
    WHERE provider_id = p_provider_id
    AND appointment_date < (p_appointment_date + (p_duration_minutes || ' minutes')::INTERVAL)
    AND (appointment_date + (duration_minutes || ' minutes')::INTERVAL) > p_appointment_date;

    IF v_conflict_count > 0 THEN
        RAISE EXCEPTION 'Scheduling conflict detected';
    END IF;

    INSERT INTO appointments (
        patient_id,
        provider_id,
        appointment_date,
        duration_minutes,
        status,
        reason,
        notes
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_appointment_date,
        p_duration_minutes,
        'scheduled'::appointment_status,
        p_reason,
        ''
    ) RETURNING id INTO v_appointment_id;

    RETURN v_appointment_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.search_medical_records(search_query text, p_patient_id uuid DEFAULT NULL::uuid, p_provider_id uuid DEFAULT NULL::uuid)
 RETURNS TABLE(id uuid, patient_id uuid, provider_id uuid, visit_date timestamp without time zone, chief_complaint text, diagnosis text[], relevance double precision)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        mr.id,
        mr.patient_id,
        mr.provider_id,
        mr.visit_date,
        mr.chief_complaint,
        mr.diagnosis,
        ts_rank(mr.search_vector, to_tsquery('english', search_query)) as relevance
    FROM medical_records mr
    WHERE 
        mr.search_vector @@ to_tsquery('english', search_query)
        AND (p_patient_id IS NULL OR mr.patient_id = p_patient_id)
        AND (p_provider_id IS NULL OR mr.provider_id = p_provider_id)
    ORDER BY relevance DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.search_patients(search_query text, p_provider_id uuid DEFAULT NULL::uuid)
 RETURNS TABLE(id uuid, first_name text, last_name text, date_of_birth date, gender gender, relevance double precision)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        p.date_of_birth,
        p.gender,
        ts_rank(p.search_vector, to_tsquery('english', search_query)) as relevance
    FROM patients p
    WHERE 
        p.search_vector @@ to_tsquery('english', search_query)
        AND (p_provider_id IS NULL OR EXISTS (
            SELECT 1 FROM medical_records mr 
            WHERE mr.patient_id = p.id 
            AND mr.provider_id = p_provider_id
        ))
    ORDER BY relevance DESC;
END;
$function$
;

create or replace view "public"."secure_analytics_daily_appointments" as  SELECT analytics_daily_appointments.organization_id,
    analytics_daily_appointments.date,
    analytics_daily_appointments.department_id,
    analytics_daily_appointments.total_appointments,
    analytics_daily_appointments.completed_appointments,
    analytics_daily_appointments.cancelled_appointments,
    analytics_daily_appointments.avg_duration
   FROM analytics_daily_appointments
  WHERE can_access_analytics(analytics_daily_appointments.organization_id);


create or replace view "public"."secure_analytics_patient_metrics" as  SELECT analytics_patient_metrics.organization_id,
    analytics_patient_metrics.total_patients,
    analytics_patient_metrics.active_patients,
    analytics_patient_metrics.avg_patient_age,
    analytics_patient_metrics.total_appointments,
    analytics_patient_metrics.total_medical_records,
    analytics_patient_metrics.total_medications
   FROM analytics_patient_metrics
  WHERE can_access_analytics(analytics_patient_metrics.organization_id);


create or replace view "public"."secure_analytics_provider_metrics" as  SELECT analytics_provider_metrics.organization_id,
    analytics_provider_metrics.provider_id,
    analytics_provider_metrics.provider_name,
    analytics_provider_metrics.role,
    analytics_provider_metrics.total_appointments,
    analytics_provider_metrics.unique_patients,
    analytics_provider_metrics.medical_records_created,
    analytics_provider_metrics.medications_prescribed
   FROM analytics_provider_metrics
  WHERE can_access_analytics(analytics_provider_metrics.organization_id);


CREATE OR REPLACE FUNCTION public.send_message(p_conversation_id uuid, p_content text, p_attachments jsonb DEFAULT NULL::jsonb, p_metadata jsonb DEFAULT NULL::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_message_id UUID;
    v_participant record;
BEGIN
    -- Verify sender is participant
    IF NOT EXISTS (
        SELECT 1 FROM conversation_participants
        WHERE conversation_id = p_conversation_id
        AND user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'User is not a participant in this conversation';
    END IF;

    -- Create message
    INSERT INTO messages (
        conversation_id,
        sender_id,
        content,
        attachments,
        metadata
    ) VALUES (
        p_conversation_id,
        auth.uid(),
        p_content,
        COALESCE(p_attachments, '[]'::jsonb),
        COALESCE(p_metadata, '{}'::jsonb)
    ) RETURNING id INTO v_message_id;

    -- Create message status for all participants
    FOR v_participant IN 
        SELECT user_id 
        FROM conversation_participants 
        WHERE conversation_id = p_conversation_id 
        AND user_id != auth.uid()
    LOOP
        INSERT INTO message_states (message_id, user_id, state)
        VALUES (v_message_id, v_participant.user_id, 'sent');

        -- Send notification to participant
        PERFORM send_notification(
            'message'::notification_type,
            v_participant.user_id,
            'New Message',
            p_content,
            jsonb_build_object(
                'conversation_id', p_conversation_id,
                'message_id', v_message_id
            )
        );
    END LOOP;

    -- Update conversation timestamp
    UPDATE conversations
    SET updated_at = CURRENT_TIMESTAMP
    WHERE id = p_conversation_id;

    RETURN v_message_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.send_notification(p_type notification_type, p_recipient_id uuid, p_title text, p_content text, p_metadata jsonb DEFAULT NULL::jsonb, p_priority notification_priority DEFAULT 'medium'::notification_priority, p_scheduled_for timestamp with time zone DEFAULT NULL::timestamp with time zone)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_notification_id UUID;
    v_org_id UUID;
BEGIN
    -- Get organization ID from recipient's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = p_recipient_id
    LIMIT 1;

    -- Create notification
    INSERT INTO notifications (
        organization_id,
        type,
        priority,
        status,
        sender_id,
        recipient_id,
        title,
        content,
        metadata,
        scheduled_for
    ) VALUES (
        v_org_id,
        p_type,
        p_priority,
        CASE 
            WHEN p_scheduled_for IS NULL OR p_scheduled_for <= CURRENT_TIMESTAMP 
            THEN 'sent'::notification_status 
            ELSE 'pending'::notification_status 
        END,
        auth.uid(),
        p_recipient_id,
        p_title,
        p_content,
        COALESCE(p_metadata, '{}'::jsonb),
        p_scheduled_for
    ) RETURNING id INTO v_notification_id;

    -- If notification is for immediate delivery, update sent_at
    IF p_scheduled_for IS NULL OR p_scheduled_for <= CURRENT_TIMESTAMP THEN
        UPDATE notifications 
        SET sent_at = CURRENT_TIMESTAMP 
        WHERE id = v_notification_id;
    END IF;

    RETURN v_notification_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.start_conversation(p_participants uuid[], p_type conversation_type DEFAULT 'direct'::conversation_type, p_title text DEFAULT NULL::text, p_initial_message text DEFAULT NULL::text, p_metadata jsonb DEFAULT NULL::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_conversation_id UUID;
    v_org_id UUID;
    v_participant UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Create conversation
    INSERT INTO conversations (
        organization_id,
        type,
        title,
        metadata,
        created_by
    ) VALUES (
        v_org_id,
        p_type,
        COALESCE(p_title, CASE 
            WHEN p_type = 'direct' THEN NULL 
            ELSE 'New Conversation' 
        END),
        COALESCE(p_metadata, '{}'::jsonb),
        auth.uid()
    ) RETURNING id INTO v_conversation_id;

    -- Add participants
    FOREACH v_participant IN ARRAY p_participants || ARRAY[auth.uid()]
    LOOP
        INSERT INTO conversation_participants (conversation_id, user_id)
        VALUES (v_conversation_id, v_participant)
        ON CONFLICT DO NOTHING;
    END LOOP;

    -- Add initial message if provided
    IF p_initial_message IS NOT NULL THEN
        INSERT INTO messages (conversation_id, sender_id, content)
        VALUES (v_conversation_id, auth.uid(), p_initial_message);
    END IF;

    RETURN v_conversation_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.start_workflow(p_workflow_id uuid, p_context jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_instance_id UUID;
BEGIN
    -- Create workflow instance
    INSERT INTO workflow_instances (
        workflow_id,
        context
    ) VALUES (
        p_workflow_id,
        p_context
    ) RETURNING id INTO v_instance_id;

    -- Log workflow start
    INSERT INTO workflow_logs (
        workflow_instance_id,
        step_number,
        step_name,
        status,
        message
    ) VALUES (
        v_instance_id,
        0,
        'workflow_start',
        'in_progress',
        'Workflow started'
    );

    RETURN v_instance_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_medical_record_search_vector()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    NEW.search_vector :=
        setweight(to_tsvector('english', coalesce(NEW.chief_complaint, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(array_to_string(NEW.diagnosis, ' '), '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.treatment_plan, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.notes, '')), 'C');
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_patient_search_vector()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('english', coalesce(NEW.first_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.last_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.email, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.phone, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.address, '')), 'C');
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_task_status(p_task_id uuid, p_status task_status, p_comment text DEFAULT NULL::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_task tasks;
BEGIN
    -- Get task details
    SELECT * INTO v_task
    FROM tasks
    WHERE id = p_task_id
    AND (assigned_to = auth.uid() OR assigned_by = auth.uid());

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Update task status
    UPDATE tasks
    SET 
        status = p_status,
        completed_at = CASE WHEN p_status = 'completed' THEN CURRENT_TIMESTAMP ELSE NULL END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_task_id;

    -- Add comment if provided
    IF p_comment IS NOT NULL THEN
        INSERT INTO task_comments (task_id, user_id, content)
        VALUES (p_task_id, auth.uid(), p_comment);
    END IF;

    -- Notify task creator if completed
    IF p_status = 'completed' AND v_task.assigned_by != auth.uid() THEN
        PERFORM send_notification(
            'task_assignment'::notification_type,
            v_task.assigned_by,
            'Task Completed: ' || v_task.title,
            'Task has been marked as completed' || COALESCE(' with comment: ' || p_comment, ''),
            jsonb_build_object('task_id', p_task_id)
        );
    END IF;

    RETURN true;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  NEW.updated_at = TIMEZONE('utc', NOW());
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_workflow_step(p_instance_id uuid, p_step_number integer, p_status workflow_status, p_result jsonb DEFAULT NULL::jsonb, p_message text DEFAULT NULL::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_workflow_instance workflow_instances;
    v_workflow workflows;
BEGIN
    -- Get workflow instance
    SELECT * INTO v_workflow_instance
    FROM workflow_instances
    WHERE id = p_instance_id
    FOR UPDATE;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Get workflow
    SELECT * INTO v_workflow
    FROM workflows
    WHERE id = v_workflow_instance.workflow_id;

    -- Update workflow instance
    UPDATE workflow_instances
    SET
        status = CASE 
            WHEN p_status = 'completed' AND p_step_number = array_length(v_workflow.steps, 1) THEN 'completed'
            WHEN p_status = 'failed' THEN 'failed'
            ELSE 'in_progress'
        END,
        current_step = CASE 
            WHEN p_status = 'completed' THEN p_step_number + 1
            ELSE p_step_number
        END,
        results = results || COALESCE(p_result, '{}'::jsonb),
        completed_at = CASE 
            WHEN p_status = 'completed' AND p_step_number = array_length(v_workflow.steps, 1) THEN CURRENT_TIMESTAMP
            ELSE completed_at
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_instance_id;

    -- Log step update
    INSERT INTO workflow_logs (
        workflow_instance_id,
        step_number,
        step_name,
        status,
        message,
        details
    ) VALUES (
        p_instance_id,
        p_step_number,
        (v_workflow.steps[p_step_number + 1]->>'name'),
        p_status,
        COALESCE(p_message, 'Step ' || p_step_number || ' ' || p_status),
        COALESCE(p_result, '{}'::jsonb)
    );

    RETURN true;
END;
$function$
;

CREATE UNIQUE INDEX idx_daily_appointments_org_date_dept ON public.analytics_daily_appointments USING btree (organization_id, date, department_id);

CREATE UNIQUE INDEX idx_patient_metrics_org ON public.analytics_patient_metrics USING btree (organization_id);

CREATE UNIQUE INDEX idx_provider_metrics_org_provider ON public.analytics_provider_metrics USING btree (organization_id, provider_id);

grant delete on table "public"."activity_logs" to "anon";

grant insert on table "public"."activity_logs" to "anon";

grant references on table "public"."activity_logs" to "anon";

grant select on table "public"."activity_logs" to "anon";

grant trigger on table "public"."activity_logs" to "anon";

grant truncate on table "public"."activity_logs" to "anon";

grant update on table "public"."activity_logs" to "anon";

grant delete on table "public"."activity_logs" to "authenticated";

grant insert on table "public"."activity_logs" to "authenticated";

grant references on table "public"."activity_logs" to "authenticated";

grant select on table "public"."activity_logs" to "authenticated";

grant trigger on table "public"."activity_logs" to "authenticated";

grant truncate on table "public"."activity_logs" to "authenticated";

grant update on table "public"."activity_logs" to "authenticated";

grant delete on table "public"."activity_logs" to "service_role";

grant insert on table "public"."activity_logs" to "service_role";

grant references on table "public"."activity_logs" to "service_role";

grant select on table "public"."activity_logs" to "service_role";

grant trigger on table "public"."activity_logs" to "service_role";

grant truncate on table "public"."activity_logs" to "service_role";

grant update on table "public"."activity_logs" to "service_role";

grant delete on table "public"."allergies" to "anon";

grant insert on table "public"."allergies" to "anon";

grant references on table "public"."allergies" to "anon";

grant select on table "public"."allergies" to "anon";

grant trigger on table "public"."allergies" to "anon";

grant truncate on table "public"."allergies" to "anon";

grant update on table "public"."allergies" to "anon";

grant delete on table "public"."allergies" to "authenticated";

grant insert on table "public"."allergies" to "authenticated";

grant references on table "public"."allergies" to "authenticated";

grant select on table "public"."allergies" to "authenticated";

grant trigger on table "public"."allergies" to "authenticated";

grant truncate on table "public"."allergies" to "authenticated";

grant update on table "public"."allergies" to "authenticated";

grant delete on table "public"."allergies" to "service_role";

grant insert on table "public"."allergies" to "service_role";

grant references on table "public"."allergies" to "service_role";

grant select on table "public"."allergies" to "service_role";

grant trigger on table "public"."allergies" to "service_role";

grant truncate on table "public"."allergies" to "service_role";

grant update on table "public"."allergies" to "service_role";

grant delete on table "public"."analytics_events" to "anon";

grant insert on table "public"."analytics_events" to "anon";

grant references on table "public"."analytics_events" to "anon";

grant select on table "public"."analytics_events" to "anon";

grant trigger on table "public"."analytics_events" to "anon";

grant truncate on table "public"."analytics_events" to "anon";

grant update on table "public"."analytics_events" to "anon";

grant delete on table "public"."analytics_events" to "authenticated";

grant insert on table "public"."analytics_events" to "authenticated";

grant references on table "public"."analytics_events" to "authenticated";

grant select on table "public"."analytics_events" to "authenticated";

grant trigger on table "public"."analytics_events" to "authenticated";

grant truncate on table "public"."analytics_events" to "authenticated";

grant update on table "public"."analytics_events" to "authenticated";

grant delete on table "public"."analytics_events" to "service_role";

grant insert on table "public"."analytics_events" to "service_role";

grant references on table "public"."analytics_events" to "service_role";

grant select on table "public"."analytics_events" to "service_role";

grant trigger on table "public"."analytics_events" to "service_role";

grant truncate on table "public"."analytics_events" to "service_role";

grant update on table "public"."analytics_events" to "service_role";

grant delete on table "public"."analytics_metrics" to "anon";

grant insert on table "public"."analytics_metrics" to "anon";

grant references on table "public"."analytics_metrics" to "anon";

grant select on table "public"."analytics_metrics" to "anon";

grant trigger on table "public"."analytics_metrics" to "anon";

grant truncate on table "public"."analytics_metrics" to "anon";

grant update on table "public"."analytics_metrics" to "anon";

grant delete on table "public"."analytics_metrics" to "authenticated";

grant insert on table "public"."analytics_metrics" to "authenticated";

grant references on table "public"."analytics_metrics" to "authenticated";

grant select on table "public"."analytics_metrics" to "authenticated";

grant trigger on table "public"."analytics_metrics" to "authenticated";

grant truncate on table "public"."analytics_metrics" to "authenticated";

grant update on table "public"."analytics_metrics" to "authenticated";

grant delete on table "public"."analytics_metrics" to "service_role";

grant insert on table "public"."analytics_metrics" to "service_role";

grant references on table "public"."analytics_metrics" to "service_role";

grant select on table "public"."analytics_metrics" to "service_role";

grant trigger on table "public"."analytics_metrics" to "service_role";

grant truncate on table "public"."analytics_metrics" to "service_role";

grant update on table "public"."analytics_metrics" to "service_role";

grant delete on table "public"."appointments" to "anon";

grant insert on table "public"."appointments" to "anon";

grant references on table "public"."appointments" to "anon";

grant select on table "public"."appointments" to "anon";

grant trigger on table "public"."appointments" to "anon";

grant truncate on table "public"."appointments" to "anon";

grant update on table "public"."appointments" to "anon";

grant delete on table "public"."appointments" to "authenticated";

grant insert on table "public"."appointments" to "authenticated";

grant references on table "public"."appointments" to "authenticated";

grant select on table "public"."appointments" to "authenticated";

grant trigger on table "public"."appointments" to "authenticated";

grant truncate on table "public"."appointments" to "authenticated";

grant update on table "public"."appointments" to "authenticated";

grant delete on table "public"."appointments" to "service_role";

grant insert on table "public"."appointments" to "service_role";

grant references on table "public"."appointments" to "service_role";

grant select on table "public"."appointments" to "service_role";

grant trigger on table "public"."appointments" to "service_role";

grant truncate on table "public"."appointments" to "service_role";

grant update on table "public"."appointments" to "service_role";

grant delete on table "public"."audit_logs" to "anon";

grant insert on table "public"."audit_logs" to "anon";

grant references on table "public"."audit_logs" to "anon";

grant select on table "public"."audit_logs" to "anon";

grant trigger on table "public"."audit_logs" to "anon";

grant truncate on table "public"."audit_logs" to "anon";

grant update on table "public"."audit_logs" to "anon";

grant delete on table "public"."audit_logs" to "authenticated";

grant insert on table "public"."audit_logs" to "authenticated";

grant references on table "public"."audit_logs" to "authenticated";

grant select on table "public"."audit_logs" to "authenticated";

grant trigger on table "public"."audit_logs" to "authenticated";

grant truncate on table "public"."audit_logs" to "authenticated";

grant update on table "public"."audit_logs" to "authenticated";

grant delete on table "public"."audit_logs" to "service_role";

grant insert on table "public"."audit_logs" to "service_role";

grant references on table "public"."audit_logs" to "service_role";

grant select on table "public"."audit_logs" to "service_role";

grant trigger on table "public"."audit_logs" to "service_role";

grant truncate on table "public"."audit_logs" to "service_role";

grant update on table "public"."audit_logs" to "service_role";

grant delete on table "public"."billing_codes" to "anon";

grant insert on table "public"."billing_codes" to "anon";

grant references on table "public"."billing_codes" to "anon";

grant select on table "public"."billing_codes" to "anon";

grant trigger on table "public"."billing_codes" to "anon";

grant truncate on table "public"."billing_codes" to "anon";

grant update on table "public"."billing_codes" to "anon";

grant delete on table "public"."billing_codes" to "authenticated";

grant insert on table "public"."billing_codes" to "authenticated";

grant references on table "public"."billing_codes" to "authenticated";

grant select on table "public"."billing_codes" to "authenticated";

grant trigger on table "public"."billing_codes" to "authenticated";

grant truncate on table "public"."billing_codes" to "authenticated";

grant update on table "public"."billing_codes" to "authenticated";

grant delete on table "public"."billing_codes" to "service_role";

grant insert on table "public"."billing_codes" to "service_role";

grant references on table "public"."billing_codes" to "service_role";

grant select on table "public"."billing_codes" to "service_role";

grant trigger on table "public"."billing_codes" to "service_role";

grant truncate on table "public"."billing_codes" to "service_role";

grant update on table "public"."billing_codes" to "service_role";

grant delete on table "public"."care_team_members" to "anon";

grant insert on table "public"."care_team_members" to "anon";

grant references on table "public"."care_team_members" to "anon";

grant select on table "public"."care_team_members" to "anon";

grant trigger on table "public"."care_team_members" to "anon";

grant truncate on table "public"."care_team_members" to "anon";

grant update on table "public"."care_team_members" to "anon";

grant delete on table "public"."care_team_members" to "authenticated";

grant insert on table "public"."care_team_members" to "authenticated";

grant references on table "public"."care_team_members" to "authenticated";

grant select on table "public"."care_team_members" to "authenticated";

grant trigger on table "public"."care_team_members" to "authenticated";

grant truncate on table "public"."care_team_members" to "authenticated";

grant update on table "public"."care_team_members" to "authenticated";

grant delete on table "public"."care_team_members" to "service_role";

grant insert on table "public"."care_team_members" to "service_role";

grant references on table "public"."care_team_members" to "service_role";

grant select on table "public"."care_team_members" to "service_role";

grant trigger on table "public"."care_team_members" to "service_role";

grant truncate on table "public"."care_team_members" to "service_role";

grant update on table "public"."care_team_members" to "service_role";

grant delete on table "public"."claims" to "anon";

grant insert on table "public"."claims" to "anon";

grant references on table "public"."claims" to "anon";

grant select on table "public"."claims" to "anon";

grant trigger on table "public"."claims" to "anon";

grant truncate on table "public"."claims" to "anon";

grant update on table "public"."claims" to "anon";

grant delete on table "public"."claims" to "authenticated";

grant insert on table "public"."claims" to "authenticated";

grant references on table "public"."claims" to "authenticated";

grant select on table "public"."claims" to "authenticated";

grant trigger on table "public"."claims" to "authenticated";

grant truncate on table "public"."claims" to "authenticated";

grant update on table "public"."claims" to "authenticated";

grant delete on table "public"."claims" to "service_role";

grant insert on table "public"."claims" to "service_role";

grant references on table "public"."claims" to "service_role";

grant select on table "public"."claims" to "service_role";

grant trigger on table "public"."claims" to "service_role";

grant truncate on table "public"."claims" to "service_role";

grant update on table "public"."claims" to "service_role";

grant delete on table "public"."clinical_notes" to "anon";

grant insert on table "public"."clinical_notes" to "anon";

grant references on table "public"."clinical_notes" to "anon";

grant select on table "public"."clinical_notes" to "anon";

grant trigger on table "public"."clinical_notes" to "anon";

grant truncate on table "public"."clinical_notes" to "anon";

grant update on table "public"."clinical_notes" to "anon";

grant delete on table "public"."clinical_notes" to "authenticated";

grant insert on table "public"."clinical_notes" to "authenticated";

grant references on table "public"."clinical_notes" to "authenticated";

grant select on table "public"."clinical_notes" to "authenticated";

grant trigger on table "public"."clinical_notes" to "authenticated";

grant truncate on table "public"."clinical_notes" to "authenticated";

grant update on table "public"."clinical_notes" to "authenticated";

grant delete on table "public"."clinical_notes" to "service_role";

grant insert on table "public"."clinical_notes" to "service_role";

grant references on table "public"."clinical_notes" to "service_role";

grant select on table "public"."clinical_notes" to "service_role";

grant trigger on table "public"."clinical_notes" to "service_role";

grant truncate on table "public"."clinical_notes" to "service_role";

grant update on table "public"."clinical_notes" to "service_role";

grant delete on table "public"."conversation_participants" to "anon";

grant insert on table "public"."conversation_participants" to "anon";

grant references on table "public"."conversation_participants" to "anon";

grant select on table "public"."conversation_participants" to "anon";

grant trigger on table "public"."conversation_participants" to "anon";

grant truncate on table "public"."conversation_participants" to "anon";

grant update on table "public"."conversation_participants" to "anon";

grant delete on table "public"."conversation_participants" to "authenticated";

grant insert on table "public"."conversation_participants" to "authenticated";

grant references on table "public"."conversation_participants" to "authenticated";

grant select on table "public"."conversation_participants" to "authenticated";

grant trigger on table "public"."conversation_participants" to "authenticated";

grant truncate on table "public"."conversation_participants" to "authenticated";

grant update on table "public"."conversation_participants" to "authenticated";

grant delete on table "public"."conversation_participants" to "service_role";

grant insert on table "public"."conversation_participants" to "service_role";

grant references on table "public"."conversation_participants" to "service_role";

grant select on table "public"."conversation_participants" to "service_role";

grant trigger on table "public"."conversation_participants" to "service_role";

grant truncate on table "public"."conversation_participants" to "service_role";

grant update on table "public"."conversation_participants" to "service_role";

grant delete on table "public"."conversations" to "anon";

grant insert on table "public"."conversations" to "anon";

grant references on table "public"."conversations" to "anon";

grant select on table "public"."conversations" to "anon";

grant trigger on table "public"."conversations" to "anon";

grant truncate on table "public"."conversations" to "anon";

grant update on table "public"."conversations" to "anon";

grant delete on table "public"."conversations" to "authenticated";

grant insert on table "public"."conversations" to "authenticated";

grant references on table "public"."conversations" to "authenticated";

grant select on table "public"."conversations" to "authenticated";

grant trigger on table "public"."conversations" to "authenticated";

grant truncate on table "public"."conversations" to "authenticated";

grant update on table "public"."conversations" to "authenticated";

grant delete on table "public"."conversations" to "service_role";

grant insert on table "public"."conversations" to "service_role";

grant references on table "public"."conversations" to "service_role";

grant select on table "public"."conversations" to "service_role";

grant trigger on table "public"."conversations" to "service_role";

grant truncate on table "public"."conversations" to "service_role";

grant update on table "public"."conversations" to "service_role";

grant delete on table "public"."departments" to "anon";

grant insert on table "public"."departments" to "anon";

grant references on table "public"."departments" to "anon";

grant select on table "public"."departments" to "anon";

grant trigger on table "public"."departments" to "anon";

grant truncate on table "public"."departments" to "anon";

grant update on table "public"."departments" to "anon";

grant delete on table "public"."departments" to "authenticated";

grant insert on table "public"."departments" to "authenticated";

grant references on table "public"."departments" to "authenticated";

grant select on table "public"."departments" to "authenticated";

grant trigger on table "public"."departments" to "authenticated";

grant truncate on table "public"."departments" to "authenticated";

grant update on table "public"."departments" to "authenticated";

grant delete on table "public"."departments" to "service_role";

grant insert on table "public"."departments" to "service_role";

grant references on table "public"."departments" to "service_role";

grant select on table "public"."departments" to "service_role";

grant trigger on table "public"."departments" to "service_role";

grant truncate on table "public"."departments" to "service_role";

grant update on table "public"."departments" to "service_role";

grant delete on table "public"."documents" to "anon";

grant insert on table "public"."documents" to "anon";

grant references on table "public"."documents" to "anon";

grant select on table "public"."documents" to "anon";

grant trigger on table "public"."documents" to "anon";

grant truncate on table "public"."documents" to "anon";

grant update on table "public"."documents" to "anon";

grant delete on table "public"."documents" to "authenticated";

grant insert on table "public"."documents" to "authenticated";

grant references on table "public"."documents" to "authenticated";

grant select on table "public"."documents" to "authenticated";

grant trigger on table "public"."documents" to "authenticated";

grant truncate on table "public"."documents" to "authenticated";

grant update on table "public"."documents" to "authenticated";

grant delete on table "public"."documents" to "service_role";

grant insert on table "public"."documents" to "service_role";

grant references on table "public"."documents" to "service_role";

grant select on table "public"."documents" to "service_role";

grant trigger on table "public"."documents" to "service_role";

grant truncate on table "public"."documents" to "service_role";

grant update on table "public"."documents" to "service_role";

grant delete on table "public"."education_materials" to "anon";

grant insert on table "public"."education_materials" to "anon";

grant references on table "public"."education_materials" to "anon";

grant select on table "public"."education_materials" to "anon";

grant trigger on table "public"."education_materials" to "anon";

grant truncate on table "public"."education_materials" to "anon";

grant update on table "public"."education_materials" to "anon";

grant delete on table "public"."education_materials" to "authenticated";

grant insert on table "public"."education_materials" to "authenticated";

grant references on table "public"."education_materials" to "authenticated";

grant select on table "public"."education_materials" to "authenticated";

grant trigger on table "public"."education_materials" to "authenticated";

grant truncate on table "public"."education_materials" to "authenticated";

grant update on table "public"."education_materials" to "authenticated";

grant delete on table "public"."education_materials" to "service_role";

grant insert on table "public"."education_materials" to "service_role";

grant references on table "public"."education_materials" to "service_role";

grant select on table "public"."education_materials" to "service_role";

grant trigger on table "public"."education_materials" to "service_role";

grant truncate on table "public"."education_materials" to "service_role";

grant update on table "public"."education_materials" to "service_role";

grant delete on table "public"."facilities" to "anon";

grant insert on table "public"."facilities" to "anon";

grant references on table "public"."facilities" to "anon";

grant select on table "public"."facilities" to "anon";

grant trigger on table "public"."facilities" to "anon";

grant truncate on table "public"."facilities" to "anon";

grant update on table "public"."facilities" to "anon";

grant delete on table "public"."facilities" to "authenticated";

grant insert on table "public"."facilities" to "authenticated";

grant references on table "public"."facilities" to "authenticated";

grant select on table "public"."facilities" to "authenticated";

grant trigger on table "public"."facilities" to "authenticated";

grant truncate on table "public"."facilities" to "authenticated";

grant update on table "public"."facilities" to "authenticated";

grant delete on table "public"."facilities" to "service_role";

grant insert on table "public"."facilities" to "service_role";

grant references on table "public"."facilities" to "service_role";

grant select on table "public"."facilities" to "service_role";

grant trigger on table "public"."facilities" to "service_role";

grant truncate on table "public"."facilities" to "service_role";

grant update on table "public"."facilities" to "service_role";

grant delete on table "public"."healthcare_providers" to "anon";

grant insert on table "public"."healthcare_providers" to "anon";

grant references on table "public"."healthcare_providers" to "anon";

grant select on table "public"."healthcare_providers" to "anon";

grant trigger on table "public"."healthcare_providers" to "anon";

grant truncate on table "public"."healthcare_providers" to "anon";

grant update on table "public"."healthcare_providers" to "anon";

grant delete on table "public"."healthcare_providers" to "authenticated";

grant insert on table "public"."healthcare_providers" to "authenticated";

grant references on table "public"."healthcare_providers" to "authenticated";

grant select on table "public"."healthcare_providers" to "authenticated";

grant trigger on table "public"."healthcare_providers" to "authenticated";

grant truncate on table "public"."healthcare_providers" to "authenticated";

grant update on table "public"."healthcare_providers" to "authenticated";

grant delete on table "public"."healthcare_providers" to "service_role";

grant insert on table "public"."healthcare_providers" to "service_role";

grant references on table "public"."healthcare_providers" to "service_role";

grant select on table "public"."healthcare_providers" to "service_role";

grant trigger on table "public"."healthcare_providers" to "service_role";

grant truncate on table "public"."healthcare_providers" to "service_role";

grant update on table "public"."healthcare_providers" to "service_role";

grant delete on table "public"."immunizations" to "anon";

grant insert on table "public"."immunizations" to "anon";

grant references on table "public"."immunizations" to "anon";

grant select on table "public"."immunizations" to "anon";

grant trigger on table "public"."immunizations" to "anon";

grant truncate on table "public"."immunizations" to "anon";

grant update on table "public"."immunizations" to "anon";

grant delete on table "public"."immunizations" to "authenticated";

grant insert on table "public"."immunizations" to "authenticated";

grant references on table "public"."immunizations" to "authenticated";

grant select on table "public"."immunizations" to "authenticated";

grant trigger on table "public"."immunizations" to "authenticated";

grant truncate on table "public"."immunizations" to "authenticated";

grant update on table "public"."immunizations" to "authenticated";

grant delete on table "public"."immunizations" to "service_role";

grant insert on table "public"."immunizations" to "service_role";

grant references on table "public"."immunizations" to "service_role";

grant select on table "public"."immunizations" to "service_role";

grant trigger on table "public"."immunizations" to "service_role";

grant truncate on table "public"."immunizations" to "service_role";

grant update on table "public"."immunizations" to "service_role";

grant delete on table "public"."insurance_providers" to "anon";

grant insert on table "public"."insurance_providers" to "anon";

grant references on table "public"."insurance_providers" to "anon";

grant select on table "public"."insurance_providers" to "anon";

grant trigger on table "public"."insurance_providers" to "anon";

grant truncate on table "public"."insurance_providers" to "anon";

grant update on table "public"."insurance_providers" to "anon";

grant delete on table "public"."insurance_providers" to "authenticated";

grant insert on table "public"."insurance_providers" to "authenticated";

grant references on table "public"."insurance_providers" to "authenticated";

grant select on table "public"."insurance_providers" to "authenticated";

grant trigger on table "public"."insurance_providers" to "authenticated";

grant truncate on table "public"."insurance_providers" to "authenticated";

grant update on table "public"."insurance_providers" to "authenticated";

grant delete on table "public"."insurance_providers" to "service_role";

grant insert on table "public"."insurance_providers" to "service_role";

grant references on table "public"."insurance_providers" to "service_role";

grant select on table "public"."insurance_providers" to "service_role";

grant trigger on table "public"."insurance_providers" to "service_role";

grant truncate on table "public"."insurance_providers" to "service_role";

grant update on table "public"."insurance_providers" to "service_role";

grant delete on table "public"."inventory_items" to "anon";

grant insert on table "public"."inventory_items" to "anon";

grant references on table "public"."inventory_items" to "anon";

grant select on table "public"."inventory_items" to "anon";

grant trigger on table "public"."inventory_items" to "anon";

grant truncate on table "public"."inventory_items" to "anon";

grant update on table "public"."inventory_items" to "anon";

grant delete on table "public"."inventory_items" to "authenticated";

grant insert on table "public"."inventory_items" to "authenticated";

grant references on table "public"."inventory_items" to "authenticated";

grant select on table "public"."inventory_items" to "authenticated";

grant trigger on table "public"."inventory_items" to "authenticated";

grant truncate on table "public"."inventory_items" to "authenticated";

grant update on table "public"."inventory_items" to "authenticated";

grant delete on table "public"."inventory_items" to "service_role";

grant insert on table "public"."inventory_items" to "service_role";

grant references on table "public"."inventory_items" to "service_role";

grant select on table "public"."inventory_items" to "service_role";

grant trigger on table "public"."inventory_items" to "service_role";

grant truncate on table "public"."inventory_items" to "service_role";

grant update on table "public"."inventory_items" to "service_role";

grant delete on table "public"."inventory_transactions" to "anon";

grant insert on table "public"."inventory_transactions" to "anon";

grant references on table "public"."inventory_transactions" to "anon";

grant select on table "public"."inventory_transactions" to "anon";

grant trigger on table "public"."inventory_transactions" to "anon";

grant truncate on table "public"."inventory_transactions" to "anon";

grant update on table "public"."inventory_transactions" to "anon";

grant delete on table "public"."inventory_transactions" to "authenticated";

grant insert on table "public"."inventory_transactions" to "authenticated";

grant references on table "public"."inventory_transactions" to "authenticated";

grant select on table "public"."inventory_transactions" to "authenticated";

grant trigger on table "public"."inventory_transactions" to "authenticated";

grant truncate on table "public"."inventory_transactions" to "authenticated";

grant update on table "public"."inventory_transactions" to "authenticated";

grant delete on table "public"."inventory_transactions" to "service_role";

grant insert on table "public"."inventory_transactions" to "service_role";

grant references on table "public"."inventory_transactions" to "service_role";

grant select on table "public"."inventory_transactions" to "service_role";

grant trigger on table "public"."inventory_transactions" to "service_role";

grant truncate on table "public"."inventory_transactions" to "service_role";

grant update on table "public"."inventory_transactions" to "service_role";

grant delete on table "public"."lab_results" to "anon";

grant insert on table "public"."lab_results" to "anon";

grant references on table "public"."lab_results" to "anon";

grant select on table "public"."lab_results" to "anon";

grant trigger on table "public"."lab_results" to "anon";

grant truncate on table "public"."lab_results" to "anon";

grant update on table "public"."lab_results" to "anon";

grant delete on table "public"."lab_results" to "authenticated";

grant insert on table "public"."lab_results" to "authenticated";

grant references on table "public"."lab_results" to "authenticated";

grant select on table "public"."lab_results" to "authenticated";

grant trigger on table "public"."lab_results" to "authenticated";

grant truncate on table "public"."lab_results" to "authenticated";

grant update on table "public"."lab_results" to "authenticated";

grant delete on table "public"."lab_results" to "service_role";

grant insert on table "public"."lab_results" to "service_role";

grant references on table "public"."lab_results" to "service_role";

grant select on table "public"."lab_results" to "service_role";

grant trigger on table "public"."lab_results" to "service_role";

grant truncate on table "public"."lab_results" to "service_role";

grant update on table "public"."lab_results" to "service_role";

grant delete on table "public"."medical_records" to "anon";

grant insert on table "public"."medical_records" to "anon";

grant references on table "public"."medical_records" to "anon";

grant select on table "public"."medical_records" to "anon";

grant trigger on table "public"."medical_records" to "anon";

grant truncate on table "public"."medical_records" to "anon";

grant update on table "public"."medical_records" to "anon";

grant delete on table "public"."medical_records" to "authenticated";

grant insert on table "public"."medical_records" to "authenticated";

grant references on table "public"."medical_records" to "authenticated";

grant select on table "public"."medical_records" to "authenticated";

grant trigger on table "public"."medical_records" to "authenticated";

grant truncate on table "public"."medical_records" to "authenticated";

grant update on table "public"."medical_records" to "authenticated";

grant delete on table "public"."medical_records" to "service_role";

grant insert on table "public"."medical_records" to "service_role";

grant references on table "public"."medical_records" to "service_role";

grant select on table "public"."medical_records" to "service_role";

grant trigger on table "public"."medical_records" to "service_role";

grant truncate on table "public"."medical_records" to "service_role";

grant update on table "public"."medical_records" to "service_role";

grant delete on table "public"."medications" to "anon";

grant insert on table "public"."medications" to "anon";

grant references on table "public"."medications" to "anon";

grant select on table "public"."medications" to "anon";

grant trigger on table "public"."medications" to "anon";

grant truncate on table "public"."medications" to "anon";

grant update on table "public"."medications" to "anon";

grant delete on table "public"."medications" to "authenticated";

grant insert on table "public"."medications" to "authenticated";

grant references on table "public"."medications" to "authenticated";

grant select on table "public"."medications" to "authenticated";

grant trigger on table "public"."medications" to "authenticated";

grant truncate on table "public"."medications" to "authenticated";

grant update on table "public"."medications" to "authenticated";

grant delete on table "public"."medications" to "service_role";

grant insert on table "public"."medications" to "service_role";

grant references on table "public"."medications" to "service_role";

grant select on table "public"."medications" to "service_role";

grant trigger on table "public"."medications" to "service_role";

grant truncate on table "public"."medications" to "service_role";

grant update on table "public"."medications" to "service_role";

grant delete on table "public"."message_states" to "anon";

grant insert on table "public"."message_states" to "anon";

grant references on table "public"."message_states" to "anon";

grant select on table "public"."message_states" to "anon";

grant trigger on table "public"."message_states" to "anon";

grant truncate on table "public"."message_states" to "anon";

grant update on table "public"."message_states" to "anon";

grant delete on table "public"."message_states" to "authenticated";

grant insert on table "public"."message_states" to "authenticated";

grant references on table "public"."message_states" to "authenticated";

grant select on table "public"."message_states" to "authenticated";

grant trigger on table "public"."message_states" to "authenticated";

grant truncate on table "public"."message_states" to "authenticated";

grant update on table "public"."message_states" to "authenticated";

grant delete on table "public"."message_states" to "service_role";

grant insert on table "public"."message_states" to "service_role";

grant references on table "public"."message_states" to "service_role";

grant select on table "public"."message_states" to "service_role";

grant trigger on table "public"."message_states" to "service_role";

grant truncate on table "public"."message_states" to "service_role";

grant update on table "public"."message_states" to "service_role";

grant delete on table "public"."messages" to "anon";

grant insert on table "public"."messages" to "anon";

grant references on table "public"."messages" to "anon";

grant select on table "public"."messages" to "anon";

grant trigger on table "public"."messages" to "anon";

grant truncate on table "public"."messages" to "anon";

grant update on table "public"."messages" to "anon";

grant delete on table "public"."messages" to "authenticated";

grant insert on table "public"."messages" to "authenticated";

grant references on table "public"."messages" to "authenticated";

grant select on table "public"."messages" to "authenticated";

grant trigger on table "public"."messages" to "authenticated";

grant truncate on table "public"."messages" to "authenticated";

grant update on table "public"."messages" to "authenticated";

grant delete on table "public"."messages" to "service_role";

grant insert on table "public"."messages" to "service_role";

grant references on table "public"."messages" to "service_role";

grant select on table "public"."messages" to "service_role";

grant trigger on table "public"."messages" to "service_role";

grant truncate on table "public"."messages" to "service_role";

grant update on table "public"."messages" to "service_role";

grant delete on table "public"."notification_preferences" to "anon";

grant insert on table "public"."notification_preferences" to "anon";

grant references on table "public"."notification_preferences" to "anon";

grant select on table "public"."notification_preferences" to "anon";

grant trigger on table "public"."notification_preferences" to "anon";

grant truncate on table "public"."notification_preferences" to "anon";

grant update on table "public"."notification_preferences" to "anon";

grant delete on table "public"."notification_preferences" to "authenticated";

grant insert on table "public"."notification_preferences" to "authenticated";

grant references on table "public"."notification_preferences" to "authenticated";

grant select on table "public"."notification_preferences" to "authenticated";

grant trigger on table "public"."notification_preferences" to "authenticated";

grant truncate on table "public"."notification_preferences" to "authenticated";

grant update on table "public"."notification_preferences" to "authenticated";

grant delete on table "public"."notification_preferences" to "service_role";

grant insert on table "public"."notification_preferences" to "service_role";

grant references on table "public"."notification_preferences" to "service_role";

grant select on table "public"."notification_preferences" to "service_role";

grant trigger on table "public"."notification_preferences" to "service_role";

grant truncate on table "public"."notification_preferences" to "service_role";

grant update on table "public"."notification_preferences" to "service_role";

grant delete on table "public"."notification_templates" to "anon";

grant insert on table "public"."notification_templates" to "anon";

grant references on table "public"."notification_templates" to "anon";

grant select on table "public"."notification_templates" to "anon";

grant trigger on table "public"."notification_templates" to "anon";

grant truncate on table "public"."notification_templates" to "anon";

grant update on table "public"."notification_templates" to "anon";

grant delete on table "public"."notification_templates" to "authenticated";

grant insert on table "public"."notification_templates" to "authenticated";

grant references on table "public"."notification_templates" to "authenticated";

grant select on table "public"."notification_templates" to "authenticated";

grant trigger on table "public"."notification_templates" to "authenticated";

grant truncate on table "public"."notification_templates" to "authenticated";

grant update on table "public"."notification_templates" to "authenticated";

grant delete on table "public"."notification_templates" to "service_role";

grant insert on table "public"."notification_templates" to "service_role";

grant references on table "public"."notification_templates" to "service_role";

grant select on table "public"."notification_templates" to "service_role";

grant trigger on table "public"."notification_templates" to "service_role";

grant truncate on table "public"."notification_templates" to "service_role";

grant update on table "public"."notification_templates" to "service_role";

grant delete on table "public"."notifications" to "anon";

grant insert on table "public"."notifications" to "anon";

grant references on table "public"."notifications" to "anon";

grant select on table "public"."notifications" to "anon";

grant trigger on table "public"."notifications" to "anon";

grant truncate on table "public"."notifications" to "anon";

grant update on table "public"."notifications" to "anon";

grant delete on table "public"."notifications" to "authenticated";

grant insert on table "public"."notifications" to "authenticated";

grant references on table "public"."notifications" to "authenticated";

grant select on table "public"."notifications" to "authenticated";

grant trigger on table "public"."notifications" to "authenticated";

grant truncate on table "public"."notifications" to "authenticated";

grant update on table "public"."notifications" to "authenticated";

grant delete on table "public"."notifications" to "service_role";

grant insert on table "public"."notifications" to "service_role";

grant references on table "public"."notifications" to "service_role";

grant select on table "public"."notifications" to "service_role";

grant trigger on table "public"."notifications" to "service_role";

grant truncate on table "public"."notifications" to "service_role";

grant update on table "public"."notifications" to "service_role";

grant delete on table "public"."orders" to "anon";

grant insert on table "public"."orders" to "anon";

grant references on table "public"."orders" to "anon";

grant select on table "public"."orders" to "anon";

grant trigger on table "public"."orders" to "anon";

grant truncate on table "public"."orders" to "anon";

grant update on table "public"."orders" to "anon";

grant delete on table "public"."orders" to "authenticated";

grant insert on table "public"."orders" to "authenticated";

grant references on table "public"."orders" to "authenticated";

grant select on table "public"."orders" to "authenticated";

grant trigger on table "public"."orders" to "authenticated";

grant truncate on table "public"."orders" to "authenticated";

grant update on table "public"."orders" to "authenticated";

grant delete on table "public"."orders" to "service_role";

grant insert on table "public"."orders" to "service_role";

grant references on table "public"."orders" to "service_role";

grant select on table "public"."orders" to "service_role";

grant trigger on table "public"."orders" to "service_role";

grant truncate on table "public"."orders" to "service_role";

grant update on table "public"."orders" to "service_role";

grant delete on table "public"."organization_invites" to "anon";

grant insert on table "public"."organization_invites" to "anon";

grant references on table "public"."organization_invites" to "anon";

grant select on table "public"."organization_invites" to "anon";

grant trigger on table "public"."organization_invites" to "anon";

grant truncate on table "public"."organization_invites" to "anon";

grant update on table "public"."organization_invites" to "anon";

grant delete on table "public"."organization_invites" to "authenticated";

grant insert on table "public"."organization_invites" to "authenticated";

grant references on table "public"."organization_invites" to "authenticated";

grant select on table "public"."organization_invites" to "authenticated";

grant trigger on table "public"."organization_invites" to "authenticated";

grant truncate on table "public"."organization_invites" to "authenticated";

grant update on table "public"."organization_invites" to "authenticated";

grant delete on table "public"."organization_invites" to "service_role";

grant insert on table "public"."organization_invites" to "service_role";

grant references on table "public"."organization_invites" to "service_role";

grant select on table "public"."organization_invites" to "service_role";

grant trigger on table "public"."organization_invites" to "service_role";

grant truncate on table "public"."organization_invites" to "service_role";

grant update on table "public"."organization_invites" to "service_role";

grant delete on table "public"."organizations" to "anon";

grant insert on table "public"."organizations" to "anon";

grant references on table "public"."organizations" to "anon";

grant select on table "public"."organizations" to "anon";

grant trigger on table "public"."organizations" to "anon";

grant truncate on table "public"."organizations" to "anon";

grant update on table "public"."organizations" to "anon";

grant delete on table "public"."organizations" to "authenticated";

grant insert on table "public"."organizations" to "authenticated";

grant references on table "public"."organizations" to "authenticated";

grant select on table "public"."organizations" to "authenticated";

grant trigger on table "public"."organizations" to "authenticated";

grant truncate on table "public"."organizations" to "authenticated";

grant update on table "public"."organizations" to "authenticated";

grant delete on table "public"."organizations" to "service_role";

grant insert on table "public"."organizations" to "service_role";

grant references on table "public"."organizations" to "service_role";

grant select on table "public"."organizations" to "service_role";

grant trigger on table "public"."organizations" to "service_role";

grant truncate on table "public"."organizations" to "service_role";

grant update on table "public"."organizations" to "service_role";

grant delete on table "public"."patient_alerts" to "anon";

grant insert on table "public"."patient_alerts" to "anon";

grant references on table "public"."patient_alerts" to "anon";

grant select on table "public"."patient_alerts" to "anon";

grant trigger on table "public"."patient_alerts" to "anon";

grant truncate on table "public"."patient_alerts" to "anon";

grant update on table "public"."patient_alerts" to "anon";

grant delete on table "public"."patient_alerts" to "authenticated";

grant insert on table "public"."patient_alerts" to "authenticated";

grant references on table "public"."patient_alerts" to "authenticated";

grant select on table "public"."patient_alerts" to "authenticated";

grant trigger on table "public"."patient_alerts" to "authenticated";

grant truncate on table "public"."patient_alerts" to "authenticated";

grant update on table "public"."patient_alerts" to "authenticated";

grant delete on table "public"."patient_alerts" to "service_role";

grant insert on table "public"."patient_alerts" to "service_role";

grant references on table "public"."patient_alerts" to "service_role";

grant select on table "public"."patient_alerts" to "service_role";

grant trigger on table "public"."patient_alerts" to "service_role";

grant truncate on table "public"."patient_alerts" to "service_role";

grant update on table "public"."patient_alerts" to "service_role";

grant delete on table "public"."patient_education_records" to "anon";

grant insert on table "public"."patient_education_records" to "anon";

grant references on table "public"."patient_education_records" to "anon";

grant select on table "public"."patient_education_records" to "anon";

grant trigger on table "public"."patient_education_records" to "anon";

grant truncate on table "public"."patient_education_records" to "anon";

grant update on table "public"."patient_education_records" to "anon";

grant delete on table "public"."patient_education_records" to "authenticated";

grant insert on table "public"."patient_education_records" to "authenticated";

grant references on table "public"."patient_education_records" to "authenticated";

grant select on table "public"."patient_education_records" to "authenticated";

grant trigger on table "public"."patient_education_records" to "authenticated";

grant truncate on table "public"."patient_education_records" to "authenticated";

grant update on table "public"."patient_education_records" to "authenticated";

grant delete on table "public"."patient_education_records" to "service_role";

grant insert on table "public"."patient_education_records" to "service_role";

grant references on table "public"."patient_education_records" to "service_role";

grant select on table "public"."patient_education_records" to "service_role";

grant trigger on table "public"."patient_education_records" to "service_role";

grant truncate on table "public"."patient_education_records" to "service_role";

grant update on table "public"."patient_education_records" to "service_role";

grant delete on table "public"."patient_portal_settings" to "anon";

grant insert on table "public"."patient_portal_settings" to "anon";

grant references on table "public"."patient_portal_settings" to "anon";

grant select on table "public"."patient_portal_settings" to "anon";

grant trigger on table "public"."patient_portal_settings" to "anon";

grant truncate on table "public"."patient_portal_settings" to "anon";

grant update on table "public"."patient_portal_settings" to "anon";

grant delete on table "public"."patient_portal_settings" to "authenticated";

grant insert on table "public"."patient_portal_settings" to "authenticated";

grant references on table "public"."patient_portal_settings" to "authenticated";

grant select on table "public"."patient_portal_settings" to "authenticated";

grant trigger on table "public"."patient_portal_settings" to "authenticated";

grant truncate on table "public"."patient_portal_settings" to "authenticated";

grant update on table "public"."patient_portal_settings" to "authenticated";

grant delete on table "public"."patient_portal_settings" to "service_role";

grant insert on table "public"."patient_portal_settings" to "service_role";

grant references on table "public"."patient_portal_settings" to "service_role";

grant select on table "public"."patient_portal_settings" to "service_role";

grant trigger on table "public"."patient_portal_settings" to "service_role";

grant truncate on table "public"."patient_portal_settings" to "service_role";

grant update on table "public"."patient_portal_settings" to "service_role";

grant delete on table "public"."patient_questionnaires" to "anon";

grant insert on table "public"."patient_questionnaires" to "anon";

grant references on table "public"."patient_questionnaires" to "anon";

grant select on table "public"."patient_questionnaires" to "anon";

grant trigger on table "public"."patient_questionnaires" to "anon";

grant truncate on table "public"."patient_questionnaires" to "anon";

grant update on table "public"."patient_questionnaires" to "anon";

grant delete on table "public"."patient_questionnaires" to "authenticated";

grant insert on table "public"."patient_questionnaires" to "authenticated";

grant references on table "public"."patient_questionnaires" to "authenticated";

grant select on table "public"."patient_questionnaires" to "authenticated";

grant trigger on table "public"."patient_questionnaires" to "authenticated";

grant truncate on table "public"."patient_questionnaires" to "authenticated";

grant update on table "public"."patient_questionnaires" to "authenticated";

grant delete on table "public"."patient_questionnaires" to "service_role";

grant insert on table "public"."patient_questionnaires" to "service_role";

grant references on table "public"."patient_questionnaires" to "service_role";

grant select on table "public"."patient_questionnaires" to "service_role";

grant trigger on table "public"."patient_questionnaires" to "service_role";

grant truncate on table "public"."patient_questionnaires" to "service_role";

grant update on table "public"."patient_questionnaires" to "service_role";

grant delete on table "public"."patients" to "anon";

grant insert on table "public"."patients" to "anon";

grant references on table "public"."patients" to "anon";

grant select on table "public"."patients" to "anon";

grant trigger on table "public"."patients" to "anon";

grant truncate on table "public"."patients" to "anon";

grant update on table "public"."patients" to "anon";

grant delete on table "public"."patients" to "authenticated";

grant insert on table "public"."patients" to "authenticated";

grant references on table "public"."patients" to "authenticated";

grant select on table "public"."patients" to "authenticated";

grant trigger on table "public"."patients" to "authenticated";

grant truncate on table "public"."patients" to "authenticated";

grant update on table "public"."patients" to "authenticated";

grant delete on table "public"."patients" to "service_role";

grant insert on table "public"."patients" to "service_role";

grant references on table "public"."patients" to "service_role";

grant select on table "public"."patients" to "service_role";

grant trigger on table "public"."patients" to "service_role";

grant truncate on table "public"."patients" to "service_role";

grant update on table "public"."patients" to "service_role";

grant delete on table "public"."referrals" to "anon";

grant insert on table "public"."referrals" to "anon";

grant references on table "public"."referrals" to "anon";

grant select on table "public"."referrals" to "anon";

grant trigger on table "public"."referrals" to "anon";

grant truncate on table "public"."referrals" to "anon";

grant update on table "public"."referrals" to "anon";

grant delete on table "public"."referrals" to "authenticated";

grant insert on table "public"."referrals" to "authenticated";

grant references on table "public"."referrals" to "authenticated";

grant select on table "public"."referrals" to "authenticated";

grant trigger on table "public"."referrals" to "authenticated";

grant truncate on table "public"."referrals" to "authenticated";

grant update on table "public"."referrals" to "authenticated";

grant delete on table "public"."referrals" to "service_role";

grant insert on table "public"."referrals" to "service_role";

grant references on table "public"."referrals" to "service_role";

grant select on table "public"."referrals" to "service_role";

grant trigger on table "public"."referrals" to "service_role";

grant truncate on table "public"."referrals" to "service_role";

grant update on table "public"."referrals" to "service_role";

grant delete on table "public"."role_definitions" to "anon";

grant insert on table "public"."role_definitions" to "anon";

grant references on table "public"."role_definitions" to "anon";

grant select on table "public"."role_definitions" to "anon";

grant trigger on table "public"."role_definitions" to "anon";

grant truncate on table "public"."role_definitions" to "anon";

grant update on table "public"."role_definitions" to "anon";

grant delete on table "public"."role_definitions" to "authenticated";

grant insert on table "public"."role_definitions" to "authenticated";

grant references on table "public"."role_definitions" to "authenticated";

grant select on table "public"."role_definitions" to "authenticated";

grant trigger on table "public"."role_definitions" to "authenticated";

grant truncate on table "public"."role_definitions" to "authenticated";

grant update on table "public"."role_definitions" to "authenticated";

grant delete on table "public"."role_definitions" to "service_role";

grant insert on table "public"."role_definitions" to "service_role";

grant references on table "public"."role_definitions" to "service_role";

grant select on table "public"."role_definitions" to "service_role";

grant trigger on table "public"."role_definitions" to "service_role";

grant truncate on table "public"."role_definitions" to "service_role";

grant update on table "public"."role_definitions" to "service_role";

grant delete on table "public"."role_permissions" to "anon";

grant insert on table "public"."role_permissions" to "anon";

grant references on table "public"."role_permissions" to "anon";

grant select on table "public"."role_permissions" to "anon";

grant trigger on table "public"."role_permissions" to "anon";

grant truncate on table "public"."role_permissions" to "anon";

grant update on table "public"."role_permissions" to "anon";

grant delete on table "public"."role_permissions" to "authenticated";

grant insert on table "public"."role_permissions" to "authenticated";

grant references on table "public"."role_permissions" to "authenticated";

grant select on table "public"."role_permissions" to "authenticated";

grant trigger on table "public"."role_permissions" to "authenticated";

grant truncate on table "public"."role_permissions" to "authenticated";

grant update on table "public"."role_permissions" to "authenticated";

grant delete on table "public"."role_permissions" to "service_role";

grant insert on table "public"."role_permissions" to "service_role";

grant references on table "public"."role_permissions" to "service_role";

grant select on table "public"."role_permissions" to "service_role";

grant trigger on table "public"."role_permissions" to "service_role";

grant truncate on table "public"."role_permissions" to "service_role";

grant update on table "public"."role_permissions" to "service_role";

grant delete on table "public"."task_comments" to "anon";

grant insert on table "public"."task_comments" to "anon";

grant references on table "public"."task_comments" to "anon";

grant select on table "public"."task_comments" to "anon";

grant trigger on table "public"."task_comments" to "anon";

grant truncate on table "public"."task_comments" to "anon";

grant update on table "public"."task_comments" to "anon";

grant delete on table "public"."task_comments" to "authenticated";

grant insert on table "public"."task_comments" to "authenticated";

grant references on table "public"."task_comments" to "authenticated";

grant select on table "public"."task_comments" to "authenticated";

grant trigger on table "public"."task_comments" to "authenticated";

grant truncate on table "public"."task_comments" to "authenticated";

grant update on table "public"."task_comments" to "authenticated";

grant delete on table "public"."task_comments" to "service_role";

grant insert on table "public"."task_comments" to "service_role";

grant references on table "public"."task_comments" to "service_role";

grant select on table "public"."task_comments" to "service_role";

grant trigger on table "public"."task_comments" to "service_role";

grant truncate on table "public"."task_comments" to "service_role";

grant update on table "public"."task_comments" to "service_role";

grant delete on table "public"."task_watchers" to "anon";

grant insert on table "public"."task_watchers" to "anon";

grant references on table "public"."task_watchers" to "anon";

grant select on table "public"."task_watchers" to "anon";

grant trigger on table "public"."task_watchers" to "anon";

grant truncate on table "public"."task_watchers" to "anon";

grant update on table "public"."task_watchers" to "anon";

grant delete on table "public"."task_watchers" to "authenticated";

grant insert on table "public"."task_watchers" to "authenticated";

grant references on table "public"."task_watchers" to "authenticated";

grant select on table "public"."task_watchers" to "authenticated";

grant trigger on table "public"."task_watchers" to "authenticated";

grant truncate on table "public"."task_watchers" to "authenticated";

grant update on table "public"."task_watchers" to "authenticated";

grant delete on table "public"."task_watchers" to "service_role";

grant insert on table "public"."task_watchers" to "service_role";

grant references on table "public"."task_watchers" to "service_role";

grant select on table "public"."task_watchers" to "service_role";

grant trigger on table "public"."task_watchers" to "service_role";

grant truncate on table "public"."task_watchers" to "service_role";

grant update on table "public"."task_watchers" to "service_role";

grant delete on table "public"."tasks" to "anon";

grant insert on table "public"."tasks" to "anon";

grant references on table "public"."tasks" to "anon";

grant select on table "public"."tasks" to "anon";

grant trigger on table "public"."tasks" to "anon";

grant truncate on table "public"."tasks" to "anon";

grant update on table "public"."tasks" to "anon";

grant delete on table "public"."tasks" to "authenticated";

grant insert on table "public"."tasks" to "authenticated";

grant references on table "public"."tasks" to "authenticated";

grant select on table "public"."tasks" to "authenticated";

grant trigger on table "public"."tasks" to "authenticated";

grant truncate on table "public"."tasks" to "authenticated";

grant update on table "public"."tasks" to "authenticated";

grant delete on table "public"."tasks" to "service_role";

grant insert on table "public"."tasks" to "service_role";

grant references on table "public"."tasks" to "service_role";

grant select on table "public"."tasks" to "service_role";

grant trigger on table "public"."tasks" to "service_role";

grant truncate on table "public"."tasks" to "service_role";

grant update on table "public"."tasks" to "service_role";

grant delete on table "public"."teams" to "anon";

grant insert on table "public"."teams" to "anon";

grant references on table "public"."teams" to "anon";

grant select on table "public"."teams" to "anon";

grant trigger on table "public"."teams" to "anon";

grant truncate on table "public"."teams" to "anon";

grant update on table "public"."teams" to "anon";

grant delete on table "public"."teams" to "authenticated";

grant insert on table "public"."teams" to "authenticated";

grant references on table "public"."teams" to "authenticated";

grant select on table "public"."teams" to "authenticated";

grant trigger on table "public"."teams" to "authenticated";

grant truncate on table "public"."teams" to "authenticated";

grant update on table "public"."teams" to "authenticated";

grant delete on table "public"."teams" to "service_role";

grant insert on table "public"."teams" to "service_role";

grant references on table "public"."teams" to "service_role";

grant select on table "public"."teams" to "service_role";

grant trigger on table "public"."teams" to "service_role";

grant truncate on table "public"."teams" to "service_role";

grant update on table "public"."teams" to "service_role";

grant delete on table "public"."templates" to "anon";

grant insert on table "public"."templates" to "anon";

grant references on table "public"."templates" to "anon";

grant select on table "public"."templates" to "anon";

grant trigger on table "public"."templates" to "anon";

grant truncate on table "public"."templates" to "anon";

grant update on table "public"."templates" to "anon";

grant delete on table "public"."templates" to "authenticated";

grant insert on table "public"."templates" to "authenticated";

grant references on table "public"."templates" to "authenticated";

grant select on table "public"."templates" to "authenticated";

grant trigger on table "public"."templates" to "authenticated";

grant truncate on table "public"."templates" to "authenticated";

grant update on table "public"."templates" to "authenticated";

grant delete on table "public"."templates" to "service_role";

grant insert on table "public"."templates" to "service_role";

grant references on table "public"."templates" to "service_role";

grant select on table "public"."templates" to "service_role";

grant trigger on table "public"."templates" to "service_role";

grant truncate on table "public"."templates" to "service_role";

grant update on table "public"."templates" to "service_role";

grant delete on table "public"."user_roles" to "anon";

grant insert on table "public"."user_roles" to "anon";

grant references on table "public"."user_roles" to "anon";

grant select on table "public"."user_roles" to "anon";

grant trigger on table "public"."user_roles" to "anon";

grant truncate on table "public"."user_roles" to "anon";

grant update on table "public"."user_roles" to "anon";

grant delete on table "public"."user_roles" to "authenticated";

grant insert on table "public"."user_roles" to "authenticated";

grant references on table "public"."user_roles" to "authenticated";

grant select on table "public"."user_roles" to "authenticated";

grant trigger on table "public"."user_roles" to "authenticated";

grant truncate on table "public"."user_roles" to "authenticated";

grant update on table "public"."user_roles" to "authenticated";

grant delete on table "public"."user_roles" to "service_role";

grant insert on table "public"."user_roles" to "service_role";

grant references on table "public"."user_roles" to "service_role";

grant select on table "public"."user_roles" to "service_role";

grant trigger on table "public"."user_roles" to "service_role";

grant truncate on table "public"."user_roles" to "service_role";

grant update on table "public"."user_roles" to "service_role";

grant delete on table "public"."vital_signs" to "anon";

grant insert on table "public"."vital_signs" to "anon";

grant references on table "public"."vital_signs" to "anon";

grant select on table "public"."vital_signs" to "anon";

grant trigger on table "public"."vital_signs" to "anon";

grant truncate on table "public"."vital_signs" to "anon";

grant update on table "public"."vital_signs" to "anon";

grant delete on table "public"."vital_signs" to "authenticated";

grant insert on table "public"."vital_signs" to "authenticated";

grant references on table "public"."vital_signs" to "authenticated";

grant select on table "public"."vital_signs" to "authenticated";

grant trigger on table "public"."vital_signs" to "authenticated";

grant truncate on table "public"."vital_signs" to "authenticated";

grant update on table "public"."vital_signs" to "authenticated";

grant delete on table "public"."vital_signs" to "service_role";

grant insert on table "public"."vital_signs" to "service_role";

grant references on table "public"."vital_signs" to "service_role";

grant select on table "public"."vital_signs" to "service_role";

grant trigger on table "public"."vital_signs" to "service_role";

grant truncate on table "public"."vital_signs" to "service_role";

grant update on table "public"."vital_signs" to "service_role";

grant delete on table "public"."workflow_instances" to "anon";

grant insert on table "public"."workflow_instances" to "anon";

grant references on table "public"."workflow_instances" to "anon";

grant select on table "public"."workflow_instances" to "anon";

grant trigger on table "public"."workflow_instances" to "anon";

grant truncate on table "public"."workflow_instances" to "anon";

grant update on table "public"."workflow_instances" to "anon";

grant delete on table "public"."workflow_instances" to "authenticated";

grant insert on table "public"."workflow_instances" to "authenticated";

grant references on table "public"."workflow_instances" to "authenticated";

grant select on table "public"."workflow_instances" to "authenticated";

grant trigger on table "public"."workflow_instances" to "authenticated";

grant truncate on table "public"."workflow_instances" to "authenticated";

grant update on table "public"."workflow_instances" to "authenticated";

grant delete on table "public"."workflow_instances" to "service_role";

grant insert on table "public"."workflow_instances" to "service_role";

grant references on table "public"."workflow_instances" to "service_role";

grant select on table "public"."workflow_instances" to "service_role";

grant trigger on table "public"."workflow_instances" to "service_role";

grant truncate on table "public"."workflow_instances" to "service_role";

grant update on table "public"."workflow_instances" to "service_role";

grant delete on table "public"."workflow_logs" to "anon";

grant insert on table "public"."workflow_logs" to "anon";

grant references on table "public"."workflow_logs" to "anon";

grant select on table "public"."workflow_logs" to "anon";

grant trigger on table "public"."workflow_logs" to "anon";

grant truncate on table "public"."workflow_logs" to "anon";

grant update on table "public"."workflow_logs" to "anon";

grant delete on table "public"."workflow_logs" to "authenticated";

grant insert on table "public"."workflow_logs" to "authenticated";

grant references on table "public"."workflow_logs" to "authenticated";

grant select on table "public"."workflow_logs" to "authenticated";

grant trigger on table "public"."workflow_logs" to "authenticated";

grant truncate on table "public"."workflow_logs" to "authenticated";

grant update on table "public"."workflow_logs" to "authenticated";

grant delete on table "public"."workflow_logs" to "service_role";

grant insert on table "public"."workflow_logs" to "service_role";

grant references on table "public"."workflow_logs" to "service_role";

grant select on table "public"."workflow_logs" to "service_role";

grant trigger on table "public"."workflow_logs" to "service_role";

grant truncate on table "public"."workflow_logs" to "service_role";

grant update on table "public"."workflow_logs" to "service_role";

grant delete on table "public"."workflows" to "anon";

grant insert on table "public"."workflows" to "anon";

grant references on table "public"."workflows" to "anon";

grant select on table "public"."workflows" to "anon";

grant trigger on table "public"."workflows" to "anon";

grant truncate on table "public"."workflows" to "anon";

grant update on table "public"."workflows" to "anon";

grant delete on table "public"."workflows" to "authenticated";

grant insert on table "public"."workflows" to "authenticated";

grant references on table "public"."workflows" to "authenticated";

grant select on table "public"."workflows" to "authenticated";

grant trigger on table "public"."workflows" to "authenticated";

grant truncate on table "public"."workflows" to "authenticated";

grant update on table "public"."workflows" to "authenticated";

grant delete on table "public"."workflows" to "service_role";

grant insert on table "public"."workflows" to "service_role";

grant references on table "public"."workflows" to "service_role";

grant select on table "public"."workflows" to "service_role";

grant trigger on table "public"."workflows" to "service_role";

grant truncate on table "public"."workflows" to "service_role";

grant update on table "public"."workflows" to "service_role";

create policy "System admins can manage all activity logs"
on "public"."activity_logs"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = auth.uid()) AND (user_roles.role = 'system_admin'::user_role)))));


create policy "Users can create activity logs for their organization"
on "public"."activity_logs"
as permissive
for insert
to public
with check ((organization_id IN ( SELECT user_roles.organization_id
   FROM user_roles
  WHERE (user_roles.user_id = auth.uid()))));


create policy "Users can view activity logs for their organization"
on "public"."activity_logs"
as permissive
for select
to public
using ((organization_id IN ( SELECT user_roles.organization_id
   FROM user_roles
  WHERE (user_roles.user_id = auth.uid()))));


create policy "Access control for allergies"
on "public"."allergies"
as permissive
for all
to authenticated
using ((EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((p.organization_id = ur.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (allergies.patient_id = p.id)))))
with check ((EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((p.organization_id = ur.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (allergies.patient_id = p.id)))));


create policy "Users can view events for their organization"
on "public"."analytics_events"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = analytics_events.organization_id) AND ((user_roles.role)::text = ANY (ARRAY['super_admin'::text, 'admin'::text]))))));


create policy "Users can view analytics for their organization"
on "public"."analytics_metrics"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = analytics_metrics.organization_id) AND ((user_roles.role)::text = ANY (ARRAY['super_admin'::text, 'admin'::text, 'staff'::text]))))));


create policy "Create appointments"
on "public"."appointments"
as permissive
for insert
to authenticated
with check (((EXISTS ( SELECT 1
   FROM patients p
  WHERE ((p.id = appointments.patient_id) AND (p.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM healthcare_providers hp
  WHERE ((hp.id = appointments.provider_id) AND (hp.user_id = ( SELECT auth.uid() AS uid)))))));


create policy "appointments_access"
on "public"."appointments"
as permissive
for select
to public
using (
CASE
    WHEN (( SELECT auth.role() AS role) = 'patient'::text) THEN (patient_id = ( SELECT auth.uid() AS uid))
    WHEN (( SELECT auth.role() AS role) = 'provider'::text) THEN (provider_id = ( SELECT auth.uid() AS uid))
    WHEN (( SELECT auth.role() AS role) = ANY (ARRAY['admin'::text, 'clinical_staff'::text])) THEN true
    ELSE false
END);


create policy "optimized_appointments_update"
on "public"."appointments"
as permissive
for update
to public
using (
CASE
    WHEN (( SELECT auth.role() AS role) = 'patient'::text) THEN (patient_id = ( SELECT auth.uid() AS uid))
    WHEN (( SELECT auth.role() AS role) = 'provider'::text) THEN (provider_id = ( SELECT auth.uid() AS uid))
    WHEN (( SELECT auth.role() AS role) = ANY (ARRAY['admin'::text, 'clinical_staff'::text])) THEN true
    ELSE false
END);


create policy "Allow read access to audit logs for healthcare providers"
on "public"."audit_logs"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))));


create policy "System admins can manage all audit logs"
on "public"."audit_logs"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = auth.uid()) AND (user_roles.role = 'system_admin'::user_role)))));


create policy "Users can view audit logs for their organization"
on "public"."audit_logs"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = auth.uid()) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role]))))));


create policy "optimized_billing_codes_access"
on "public"."billing_codes"
as permissive
for select
to public
using (
CASE
    WHEN (( SELECT auth.role() AS role) = 'admin'::text) THEN true
    WHEN (( SELECT auth.role() AS role) = ANY (ARRAY['provider'::text, 'clinical_staff'::text, 'billing_staff'::text])) THEN true
    ELSE false
END);


create policy "optimized_billing_codes_delete"
on "public"."billing_codes"
as permissive
for delete
to public
using ((( SELECT auth.role() AS role) = 'admin'::text));


create policy "optimized_billing_codes_manage"
on "public"."billing_codes"
as permissive
for insert
to public
with check ((( SELECT auth.role() AS role) = 'admin'::text));


create policy "optimized_billing_codes_update"
on "public"."billing_codes"
as permissive
for update
to public
using ((( SELECT auth.role() AS role) = 'admin'::text));


create policy "optimized_care_team_access"
on "public"."care_team_members"
as permissive
for select
to public
using (
CASE
    WHEN (( SELECT auth.role() AS role) = 'patient'::text) THEN (patient_id = ( SELECT auth.uid() AS uid))
    WHEN (( SELECT auth.role() AS role) = 'provider'::text) THEN (provider_id = ( SELECT auth.uid() AS uid))
    WHEN (( SELECT auth.role() AS role) = 'clinical_staff'::text) THEN true
    ELSE false
END);


create policy "optimized_care_team_delete"
on "public"."care_team_members"
as permissive
for delete
to public
using ((( SELECT auth.role() AS role) = 'clinical_staff'::text));


create policy "optimized_care_team_insert"
on "public"."care_team_members"
as permissive
for insert
to public
with check ((( SELECT auth.role() AS role) = 'clinical_staff'::text));


create policy "optimized_care_team_update"
on "public"."care_team_members"
as permissive
for update
to public
using ((( SELECT auth.role() AS role) = 'clinical_staff'::text));


create policy "optimized_claims_access"
on "public"."claims"
as permissive
for select
to public
using (((patient_id IN ( SELECT patients.id
   FROM patients
  WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'billing_staff'::user_role])))))));


create policy "optimized_claims_delete"
on "public"."claims"
as permissive
for delete
to public
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'billing_staff'::user_role]))))));


create policy "optimized_claims_manage"
on "public"."claims"
as permissive
for insert
to public
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'billing_staff'::user_role]))))));


create policy "optimized_claims_update"
on "public"."claims"
as permissive
for update
to public
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'billing_staff'::user_role]))))));


create policy "optimized_clinical_notes_access"
on "public"."clinical_notes"
as permissive
for select
to public
using (((signed_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM medical_records mr
  WHERE ((mr.id = clinical_notes.medical_record_id) AND ((mr.patient_id IN ( SELECT patients.id
           FROM patients
          WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (mr.provider_id IN ( SELECT healthcare_providers.id
           FROM healthcare_providers
          WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
           FROM user_roles
          WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role])))))))))));


create policy "optimized_clinical_notes_delete"
on "public"."clinical_notes"
as permissive
for delete
to public
using ((signed_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))));


create policy "optimized_clinical_notes_insert"
on "public"."clinical_notes"
as permissive
for insert
to public
with check ((signed_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))));


create policy "optimized_clinical_notes_update"
on "public"."clinical_notes"
as permissive
for update
to public
using ((signed_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))));


create policy "Consolidated access for conversation_participants"
on "public"."conversation_participants"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'staff'::text]))))) OR (user_id = ( SELECT auth.uid() AS uid))))
with check (((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'staff'::text]))))) OR (user_id = ( SELECT auth.uid() AS uid))));


create policy "Users can view conversations they're part of"
on "public"."conversations"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM conversation_participants
  WHERE ((conversation_participants.conversation_id = conversations.id) AND (conversation_participants.user_id = ( SELECT auth.uid() AS uid))))));


create policy "departments_access"
on "public"."departments"
as permissive
for all
to authenticated
using (check_user_permission(( SELECT auth.uid() AS uid), 'departments'::text, 'read'::text, jsonb_build_object('org_id', ( SELECT facilities.organization_id
   FROM facilities
  WHERE (facilities.id = departments.facility_id)))))
with check (check_user_permission(( SELECT auth.uid() AS uid), 'departments'::text, 'update'::text, jsonb_build_object('org_id', ( SELECT facilities.organization_id
   FROM facilities
  WHERE (facilities.id = departments.facility_id)))));


create policy "optimized_documents_access"
on "public"."documents"
as permissive
for select
to public
using (((patient_id IN ( SELECT patients.id
   FROM patients
  WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = documents.organization_id) AND (user_roles.role <> 'patient'::user_role))))));


create policy "optimized_documents_delete"
on "public"."documents"
as permissive
for delete
to public
using (((created_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = documents.organization_id) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role])))))));


create policy "optimized_documents_insert"
on "public"."documents"
as permissive
for insert
to public
with check (((created_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = documents.organization_id) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role])))))));


create policy "optimized_documents_update"
on "public"."documents"
as permissive
for update
to public
using (((created_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = documents.organization_id) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role])))))));


create policy "Consolidated select access for education_materials"
on "public"."education_materials"
as permissive
for select
to public
using (true);


create policy "Delete access for education_materials by staff"
on "public"."education_materials"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'super_admin'::text]))))));


create policy "Insert access for education_materials by staff"
on "public"."education_materials"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'super_admin'::text]))))));


create policy "Update access for education_materials by staff"
on "public"."education_materials"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'super_admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'super_admin'::text]))))));


create policy "facilities_access"
on "public"."facilities"
as permissive
for all
to authenticated
using (check_user_permission(( SELECT auth.uid() AS uid), 'facilities'::text, 'read'::text, jsonb_build_object('org_id', organization_id)))
with check (check_user_permission(( SELECT auth.uid() AS uid), 'facilities'::text, 'update'::text, jsonb_build_object('org_id', organization_id)));


create policy "Providers can update their own profile"
on "public"."healthcare_providers"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "optimized_healthcare_providers_access"
on "public"."healthcare_providers"
as permissive
for select
to public
using (((user_id = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = healthcare_providers.organization_id) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role])))))));


create policy "Consolidated access for immunizations"
on "public"."immunizations"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM patients
  WHERE ((patients.id = immunizations.patient_id) AND (patients.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN user_roles ur ON ((hp.user_id = ur.user_id)))
  WHERE ((hp.id = immunizations.administered_by) AND (ur.user_id = ( SELECT auth.uid() AS uid)) AND ((ur.role)::text = ANY (ARRAY['provider'::text, 'clinical_staff'::text, 'admin'::text, 'super_admin'::text])))))))
with check ((EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN user_roles ur ON ((hp.user_id = ur.user_id)))
  WHERE ((hp.id = immunizations.administered_by) AND (ur.user_id = ( SELECT auth.uid() AS uid)) AND ((ur.role)::text = ANY (ARRAY['provider'::text, 'clinical_staff'::text, 'admin'::text, 'super_admin'::text]))))));


create policy "Consolidated view access for insurance_providers"
on "public"."insurance_providers"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'staff'::text, 'billing'::text, 'clinical_staff'::text, 'provider'::text, 'super_admin'::text]))))));


create policy "Delete access for insurance_providers by admin_billing"
on "public"."insurance_providers"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'billing'::text, 'super_admin'::text]))))));


create policy "Insert access for insurance_providers by admin_billing"
on "public"."insurance_providers"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'billing'::text, 'super_admin'::text]))))));


create policy "Update access for insurance_providers by admin_billing"
on "public"."insurance_providers"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'billing'::text, 'super_admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'billing'::text, 'super_admin'::text]))))));


create policy "Consolidated access for inventory_items"
on "public"."inventory_items"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (ur.organization_id = inventory_items.organization_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'staff'::text, 'super_admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (ur.organization_id = inventory_items.organization_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text]))))));


create policy "Consolidated access for inventory_transactions"
on "public"."inventory_transactions"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN inventory_items ii ON ((ur.organization_id = ii.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (ii.id = inventory_transactions.item_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'staff'::text, 'super_admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN inventory_items ii ON ((ur.organization_id = ii.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (ii.id = inventory_transactions.item_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'staff'::text, 'super_admin'::text]))))));


create policy "Create lab results"
on "public"."lab_results"
as permissive
for insert
to authenticated
with check (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))));


create policy "Update lab results"
on "public"."lab_results"
as permissive
for update
to authenticated
using (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))))
with check (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))));


create policy "optimized_lab_results_access"
on "public"."lab_results"
as permissive
for select
to public
using (((patient_id IN ( SELECT patients.id
   FROM patients
  WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN care_team_members ctm ON ((hp.id = ctm.provider_id)))
  WHERE ((hp.user_id = ( SELECT auth.uid() AS uid)) AND (ctm.patient_id = lab_results.patient_id))))));


create policy "Create medical records"
on "public"."medical_records"
as permissive
for insert
to authenticated
with check (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))));


create policy "Update medical records"
on "public"."medical_records"
as permissive
for update
to authenticated
using (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))))
with check (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))));


create policy "optimized_medical_records_access"
on "public"."medical_records"
as permissive
for select
to public
using (((patient_id IN ( SELECT patients.id
   FROM patients
  WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role])))))));


create policy "Create medications"
on "public"."medications"
as permissive
for insert
to authenticated
with check (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))));


create policy "Update medications"
on "public"."medications"
as permissive
for update
to authenticated
using (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))))
with check (((provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) AND is_healthcare_provider(( SELECT auth.uid() AS uid))));


create policy "optimized_medications_access"
on "public"."medications"
as permissive
for select
to public
using (((patient_id IN ( SELECT patients.id
   FROM patients
  WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN care_team_members ctm ON ((hp.id = ctm.provider_id)))
  WHERE ((hp.user_id = ( SELECT auth.uid() AS uid)) AND (ctm.patient_id = medications.patient_id))))));


create policy "Users can update their message states"
on "public"."message_states"
as permissive
for update
to authenticated
using ((user_id = ( SELECT auth.uid() AS uid)))
with check ((user_id = ( SELECT auth.uid() AS uid)));


create policy "Users can view their message states"
on "public"."message_states"
as permissive
for select
to authenticated
using ((user_id = ( SELECT auth.uid() AS uid)));


create policy "Users can send messages to their conversations"
on "public"."messages"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM conversation_participants
  WHERE ((conversation_participants.conversation_id = messages.conversation_id) AND (conversation_participants.user_id = ( SELECT auth.uid() AS uid))))));


create policy "Users can view messages in their conversations"
on "public"."messages"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM conversation_participants
  WHERE ((conversation_participants.conversation_id = messages.conversation_id) AND (conversation_participants.user_id = ( SELECT auth.uid() AS uid))))));


create policy "Users can manage their notification preferences"
on "public"."notification_preferences"
as permissive
for all
to authenticated
using ((user_id = ( SELECT auth.uid() AS uid)))
with check ((user_id = ( SELECT auth.uid() AS uid)));


create policy "Organization admins can manage notification templates"
on "public"."notification_templates"
as permissive
for all
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = notification_templates.organization_id) AND ((user_roles.role)::text = ANY (ARRAY['super_admin'::text, 'admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = notification_templates.organization_id) AND ((user_roles.role)::text = ANY (ARRAY['super_admin'::text, 'admin'::text]))))));


create policy "Users can update their own notifications"
on "public"."notifications"
as permissive
for update
to authenticated
using ((recipient_id = ( SELECT auth.uid() AS uid)))
with check ((recipient_id = ( SELECT auth.uid() AS uid)));


create policy "Users can view their own notifications"
on "public"."notifications"
as permissive
for select
to authenticated
using ((recipient_id = ( SELECT auth.uid() AS uid)));


create policy "Consolidated access for orders"
on "public"."orders"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM healthcare_providers hp
  WHERE ((hp.id = orders.ordering_provider_id) AND (hp.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM patients p
  WHERE ((p.id = orders.patient_id) AND (p.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p_org ON ((ur.organization_id = p_org.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p_org.id = orders.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))))
with check (((EXISTS ( SELECT 1
   FROM healthcare_providers hp
  WHERE ((hp.id = orders.ordering_provider_id) AND (hp.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p_org ON ((ur.organization_id = p_org.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p_org.id = orders.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))));


create policy "Admins can create invites"
on "public"."organization_invites"
as permissive
for insert
to public
with check ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.organization_id = organization_invites.organization_id) AND (ur.user_id = auth.uid()) AND (ur.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role]))))));


create policy "Users can update their own invites or admins can update any"
on "public"."organization_invites"
as permissive
for update
to public
using (((email = (auth.jwt() ->> 'email'::text)) OR (EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.organization_id = organization_invites.organization_id) AND (ur.user_id = auth.uid()) AND (ur.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role])))))));


create policy "Users can view their own invites"
on "public"."organization_invites"
as permissive
for select
to public
using (((auth.jwt() IS NOT NULL) AND ((email = (auth.jwt() ->> 'email'::text)) OR (invited_by = auth.uid()) OR (EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.organization_id = organization_invites.organization_id) AND (ur.user_id = auth.uid()) AND (ur.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role]))))))));


create policy "organizations_policy"
on "public"."organizations"
as permissive
for all
to authenticated
using (true)
with check (true);


create policy "Consolidated access for patient_alerts"
on "public"."patient_alerts"
as permissive
for all
to public
using (((created_by = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_alerts.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))))
with check (((created_by = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_alerts.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))));


create policy "Consolidated access for patient_education_records"
on "public"."patient_education_records"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM patients
  WHERE ((patients.id = patient_education_records.patient_id) AND (patients.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM healthcare_providers hp
  WHERE ((hp.id = patient_education_records.provider_id) AND (hp.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_education_records.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))))
with check (((EXISTS ( SELECT 1
   FROM healthcare_providers hp
  WHERE ((hp.id = patient_education_records.provider_id) AND (hp.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_education_records.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))));


create policy "Consolidated access for patient_portal_settings"
on "public"."patient_portal_settings"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM patients
  WHERE ((patients.id = patient_portal_settings.patient_id) AND (patients.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_portal_settings.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text])))))))
with check (((EXISTS ( SELECT 1
   FROM patients
  WHERE ((patients.id = patient_portal_settings.patient_id) AND (patients.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_portal_settings.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text])))))));


create policy "Consolidated access for patient_questionnaires"
on "public"."patient_questionnaires"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM patients
  WHERE ((patients.id = patient_questionnaires.patient_id) AND (patients.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN care_team_members ctm ON ((hp.id = ctm.provider_id)))
  WHERE ((ctm.patient_id = patient_questionnaires.patient_id) AND (hp.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_questionnaires.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))))
with check (((EXISTS ( SELECT 1
   FROM patients
  WHERE ((patients.id = patient_questionnaires.patient_id) AND (patients.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN care_team_members ctm ON ((hp.id = ctm.provider_id)))
  WHERE ((ctm.patient_id = patient_questionnaires.patient_id) AND (hp.user_id = ( SELECT auth.uid() AS uid))))) OR (EXISTS ( SELECT 1
   FROM (user_roles ur
     JOIN patients p ON ((ur.organization_id = p.organization_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (p.id = patient_questionnaires.patient_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'clinical_staff'::text, 'staff'::text, 'super_admin'::text])))))));


create policy "Patients can update their own profile"
on "public"."patients"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "optimized_patients_access"
on "public"."patients"
as permissive
for select
to public
using (((user_id = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN care_team_members ctm ON ((hp.id = ctm.provider_id)))
  WHERE ((hp.user_id = ( SELECT auth.uid() AS uid)) AND (ctm.patient_id = patients.id)))) OR (EXISTS ( SELECT 1
   FROM (appointments a
     JOIN healthcare_providers hp ON ((a.provider_id = hp.id)))
  WHERE ((hp.user_id = ( SELECT auth.uid() AS uid)) AND (a.patient_id = patients.id))))));


create policy "optimized_referrals_access"
on "public"."referrals"
as permissive
for select
to public
using (((patient_id IN ( SELECT patients.id
   FROM patients
  WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (referring_provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (referred_to_provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid))))));


create policy "optimized_referrals_delete"
on "public"."referrals"
as permissive
for delete
to public
using ((referring_provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))));


create policy "optimized_referrals_manage"
on "public"."referrals"
as permissive
for insert
to public
with check ((referring_provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))));


create policy "optimized_referrals_update"
on "public"."referrals"
as permissive
for update
to public
using (((referring_provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))) OR (referred_to_provider_id IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid))))));


create policy "Consolidated view for role_permissions"
on "public"."role_permissions"
as permissive
for select
to public
using (true);


create policy "Delete access for role_permissions by admin"
on "public"."role_permissions"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text]))))));


create policy "Insert access for role_permissions by admin"
on "public"."role_permissions"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text]))))));


create policy "Update access for role_permissions by admin"
on "public"."role_permissions"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND ((user_roles.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text]))))));


create policy "Users can add comments to tasks they have access to"
on "public"."task_comments"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM tasks
  WHERE ((tasks.id = task_comments.task_id) AND ((tasks.assigned_to = ( SELECT auth.uid() AS uid)) OR (tasks.assigned_by = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
           FROM task_watchers
          WHERE ((task_watchers.task_id = tasks.id) AND (task_watchers.user_id = ( SELECT auth.uid() AS uid))))))))));


create policy "Users can view task comments they have access to"
on "public"."task_comments"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM tasks
  WHERE ((tasks.id = task_comments.task_id) AND ((tasks.assigned_to = ( SELECT auth.uid() AS uid)) OR (tasks.assigned_by = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
           FROM task_watchers
          WHERE ((task_watchers.task_id = tasks.id) AND (task_watchers.user_id = ( SELECT auth.uid() AS uid))))))))));


create policy "Consolidated access for task_watchers"
on "public"."task_watchers"
as permissive
for all
to public
using (((user_id = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM tasks
  WHERE ((tasks.id = task_watchers.task_id) AND ((tasks.assigned_to = ( SELECT auth.uid() AS uid)) OR (tasks.assigned_by = ( SELECT auth.uid() AS uid))))))))
with check (((user_id = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM tasks
  WHERE ((tasks.id = task_watchers.task_id) AND ((tasks.assigned_to = ( SELECT auth.uid() AS uid)) OR (tasks.assigned_by = ( SELECT auth.uid() AS uid))))))));


create policy "Users can update tasks they're assigned to"
on "public"."tasks"
as permissive
for update
to authenticated
using ((assigned_to = ( SELECT auth.uid() AS uid)))
with check ((assigned_to = ( SELECT auth.uid() AS uid)));


create policy "Users can view tasks they're involved with"
on "public"."tasks"
as permissive
for select
to authenticated
using (((assigned_to = ( SELECT auth.uid() AS uid)) OR (assigned_by = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM task_watchers
  WHERE ((task_watchers.task_id = tasks.id) AND (task_watchers.user_id = ( SELECT auth.uid() AS uid)))))));


create policy "Consolidated access for teams"
on "public"."teams"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM ((user_roles ur
     JOIN facilities f ON ((ur.organization_id = f.organization_id)))
     JOIN departments d ON ((f.id = d.facility_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (d.id = teams.department_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'staff'::text, 'super_admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM ((user_roles ur
     JOIN facilities f ON ((ur.organization_id = f.organization_id)))
     JOIN departments d ON ((f.id = d.facility_id)))
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (d.id = teams.department_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'super_admin'::text]))))));


create policy "Consolidated access for templates"
on "public"."templates"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (ur.organization_id = templates.organization_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'staff'::text, 'super_admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.user_id = ( SELECT auth.uid() AS uid)) AND (ur.organization_id = templates.organization_id) AND ((ur.role)::text = ANY (ARRAY['admin'::text, 'staff'::text, 'super_admin'::text]))))));


create policy "user_roles_policy"
on "public"."user_roles"
as permissive
for all
to authenticated
using (true)
with check (true);


create policy "optimized_vital_signs_insert"
on "public"."vital_signs"
as permissive
for insert
to public
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role, 'clinical_admin'::user_role, 'physician'::user_role, 'nurse_practitioner'::user_role, 'registered_nurse'::user_role, 'medical_assistant'::user_role]))))));


create policy "optimized_vital_signs_select_access"
on "public"."vital_signs"
as permissive
for select
to public
using (((patient_id IN ( SELECT patients.id
   FROM patients
  WHERE (patients.user_id = ( SELECT auth.uid() AS uid)))) OR (EXISTS ( SELECT 1
   FROM (healthcare_providers hp
     JOIN care_team_members ctm ON ((hp.id = ctm.provider_id)))
  WHERE ((hp.user_id = ( SELECT auth.uid() AS uid)) AND (ctm.patient_id = vital_signs.patient_id))))));


create policy "optimized_vital_signs_update"
on "public"."vital_signs"
as permissive
for update
to public
using ((recorded_by IN ( SELECT healthcare_providers.id
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid)))));


create policy "Users can view workflow instances for their organization"
on "public"."workflow_instances"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM (workflows w
     JOIN user_roles ur ON ((ur.organization_id = w.organization_id)))
  WHERE ((w.id = workflow_instances.workflow_id) AND (ur.user_id = ( SELECT auth.uid() AS uid))))));


create policy "Users can view workflow logs for their organization"
on "public"."workflow_logs"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM ((workflow_instances wi
     JOIN workflows w ON ((w.id = wi.workflow_id)))
     JOIN user_roles ur ON ((ur.organization_id = w.organization_id)))
  WHERE ((wi.id = workflow_logs.workflow_instance_id) AND (ur.user_id = ( SELECT auth.uid() AS uid))))));


create policy "Organization admins can manage workflows"
on "public"."workflows"
as permissive
for all
to authenticated
using ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = workflows.organization_id) AND ((user_roles.role)::text = ANY (ARRAY['super_admin'::text, 'admin'::text]))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.organization_id = workflows.organization_id) AND ((user_roles.role)::text = ANY (ARRAY['super_admin'::text, 'admin'::text]))))));


CREATE TRIGGER audit_log_changes_trigger AFTER INSERT OR DELETE OR UPDATE ON public.healthcare_providers FOR EACH ROW EXECUTE FUNCTION audit_log_changes();

CREATE TRIGGER medical_record_search_vector_update BEFORE INSERT OR UPDATE ON public.medical_records FOR EACH ROW EXECUTE FUNCTION update_medical_record_search_vector();

CREATE TRIGGER update_organization_invites_updated_at BEFORE UPDATE ON public.organization_invites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON public.organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER patient_search_vector_update BEFORE INSERT OR UPDATE ON public.patients FOR EACH ROW EXECUTE FUNCTION update_patient_search_vector();

CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON public.user_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


