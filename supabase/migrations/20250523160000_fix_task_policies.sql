-- Fix the circular dependency in RLS policies for tasks and task_watchers

-- First, drop the existing policies
DROP POLICY IF EXISTS "Users can view tasks they're involved with" ON public.tasks;
DROP POLICY IF EXISTS "Consolidated access for task_watchers" ON public.task_watchers;

-- Create a new policy for tasks that doesn't reference task_watchers
CREATE POLICY "Users can view tasks for their organization" 
ON public.tasks
FOR SELECT
TO authenticated
USING (
  -- Allow access if the user is assigned to or created the task
  (assigned_to = auth.uid() OR assigned_by = auth.uid())
  OR
  -- Allow access if the task belongs to an organization the user is part of
  (organization_id IN (
    SELECT organization_id 
    FROM user_roles 
    WHERE user_id = auth.uid()
  ))
);

-- Create a new policy for task_watchers that doesn't create a circular dependency
CREATE POLICY "Users can access task watchers for their tasks" 
ON public.task_watchers
FOR ALL
TO authenticated
USING (
  -- Allow access if the user is the watcher
  user_id = auth.uid()
  OR
  -- Allow access if the task belongs to an organization the user is part of
  (task_id IN (
    SELECT t.id
    FROM tasks t
    JOIN user_roles ur ON t.organization_id = ur.organization_id
    WHERE ur.user_id = auth.uid()
  ))
)
WITH CHECK (
  -- Allow changes if the user is the watcher
  user_id = auth.uid()
  OR
  -- Allow changes if the task belongs to an organization the user is part of
  (task_id IN (
    SELECT t.id
    FROM tasks t
    JOIN user_roles ur ON t.organization_id = ur.organization_id
    WHERE ur.user_id = auth.uid()
  ))
);
