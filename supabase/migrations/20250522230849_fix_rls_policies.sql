drop policy "System admins can manage all activity logs" on "public"."activity_logs";

drop policy "Users can create activity logs for their organization" on "public"."activity_logs";

drop policy "Users can view activity logs for their organization" on "public"."activity_logs";

drop policy "Allow read access to audit logs for healthcare providers" on "public"."audit_logs";

drop policy "System admins can manage all audit logs" on "public"."audit_logs";

drop policy "Users can view audit logs for their organization" on "public"."audit_logs";

drop policy "Admins can create invites" on "public"."organization_invites";

drop policy "Users can update their own invites or admins can update any" on "public"."organization_invites";

drop policy "Users can view their own invites" on "public"."organization_invites";

create policy "Activity logs access policy"
on "public"."activity_logs"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = 'system_admin'::user_role)))) OR (organization_id IN ( SELECT user_roles.organization_id
   FROM user_roles
  WHERE (user_roles.user_id = ( SELECT auth.uid() AS uid))))))
with check (((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = 'system_admin'::user_role)))) OR (organization_id IN ( SELECT user_roles.organization_id
   FROM user_roles
  WHERE (user_roles.user_id = ( SELECT auth.uid() AS uid))))));


create policy "Audit logs access policy"
on "public"."audit_logs"
as permissive
for all
to public
using (((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = 'system_admin'::user_role)))) OR (EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = ANY (ARRAY['org_admin'::user_role, 'clinical_admin'::user_role]))))) OR (EXISTS ( SELECT 1
   FROM healthcare_providers
  WHERE (healthcare_providers.user_id = ( SELECT auth.uid() AS uid))))))
with check ((EXISTS ( SELECT 1
   FROM user_roles
  WHERE ((user_roles.user_id = ( SELECT auth.uid() AS uid)) AND (user_roles.role = 'system_admin'::user_role)))));


create policy "Admins can create invites"
on "public"."organization_invites"
as permissive
for insert
to public
with check ((EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.organization_id = ur.organization_id) AND (ur.user_id = ( SELECT auth.uid() AS uid)) AND (ur.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role]))))));


create policy "Users can update their own invites or admins can update any"
on "public"."organization_invites"
as permissive
for update
to public
using (((email = (( SELECT auth.jwt() AS jwt) ->> 'email'::text)) OR (EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.organization_id = organization_invites.organization_id) AND (ur.user_id = ( SELECT auth.uid() AS uid)) AND (ur.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role])))))));


create policy "Users can view their own invites"
on "public"."organization_invites"
as permissive
for select
to public
using (((( SELECT auth.jwt() AS jwt) IS NOT NULL) AND ((email = (( SELECT auth.jwt() AS jwt) ->> 'email'::text)) OR (invited_by = ( SELECT auth.uid() AS uid)) OR (EXISTS ( SELECT 1
   FROM user_roles ur
  WHERE ((ur.organization_id = organization_invites.organization_id) AND (ur.user_id = ( SELECT auth.uid() AS uid)) AND (ur.role = ANY (ARRAY['system_admin'::user_role, 'org_admin'::user_role]))))))));



