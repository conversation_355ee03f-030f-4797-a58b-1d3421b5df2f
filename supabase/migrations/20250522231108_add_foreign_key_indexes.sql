CREATE INDEX idx_analytics_events_organization_id ON public.analytics_events USING btree (organization_id);

CREATE INDEX idx_analytics_metrics_organization_id ON public.analytics_metrics USING btree (organization_id);

CREATE INDEX idx_appointments_patient_id ON public.appointments USING btree (patient_id);

CREATE INDEX idx_appointments_provider_id ON public.appointments USING btree (provider_id);

CREATE INDEX idx_conversation_participants_conversation_id ON public.conversation_participants USING btree (conversation_id);

CREATE INDEX idx_lab_results_patient_id ON public.lab_results USING btree (patient_id);

CREATE INDEX idx_medical_records_patient_id ON public.medical_records USING btree (patient_id);

CREATE INDEX idx_medical_records_provider_id ON public.medical_records USING btree (provider_id);

CREATE INDEX idx_medications_patient_id ON public.medications USING btree (patient_id);

CREATE INDEX idx_medications_provider_id ON public.medications USING btree (provider_id);

CREATE INDEX idx_message_states_message_id ON public.message_states USING btree (message_id);

CREATE INDEX idx_notification_preferences_user_id ON public.notification_preferences USING btree (user_id);

CREATE INDEX idx_notification_templates_organization_id ON public.notification_templates USING btree (organization_id);

CREATE INDEX idx_organization_invites_invited_by ON public.organization_invites USING btree (invited_by);

CREATE INDEX idx_organizations_owner_id ON public.organizations USING btree (owner_id);

CREATE INDEX idx_role_permissions_role ON public.role_permissions USING btree (role);

CREATE INDEX idx_task_watchers_task_id ON public.task_watchers USING btree (task_id);

CREATE INDEX idx_user_roles_role ON public.user_roles USING btree (role);


