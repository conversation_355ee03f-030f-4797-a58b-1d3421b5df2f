#!/usr/bin/env node

/**
 * Comprehensive Seed Data Generator for Spritely Medical Application
 *
 * This script generates extensive, realistic medical data for all organizations
 * using Faker.js to create a robust demo environment that showcases all features.
 */

import { faker } from '@faker-js/faker';
import fs from 'fs';

// Set seed for reproducible results
faker.seed(12345);

// Medical specialties and their corresponding organization types
const MEDICAL_SPECIALTIES = {
  'Family Medicine': ['General Practitioner', 'Family Medicine Physician', 'Nurse Practitioner', 'Physician Assistant'],
  'Cardiology': ['Cardiologist', 'Cardiac Surgeon', 'Interventional Cardiologist', 'Cardiac Nurse'],
  'Pediatrics': ['Pediatrician', 'Pediatric Nurse', 'Child Psychologist', 'Pediatric Specialist'],
  'Orthopedics': ['Orthopedic Surgeon', 'Sports Medicine Physician', 'Physical Therapist', 'Orthopedic Nurse'],
  'Dermatology': ['Dermatologist', 'Cosmetic Surgeon', 'Dermatology Nurse', '<PERSON><PERSON>'],
  'Neurology': ['Neurologist', 'Neurosurgeon', 'Neuropsychologist', 'Neuro Nurse'],
  'Emergency Medicine': ['Emergency Physician', 'Trauma Surgeon', 'Emergency Nurse', 'Paramedic'],
  'Internal Medicine': ['Internist', 'Hospitalist', 'Internal Medicine Nurse', 'Clinical Specialist'],
  'Obstetrics': ['Obstetrician', 'Gynecologist', 'Midwife', 'Women\'s Health Nurse'],
  'Urgent Care': ['Urgent Care Physician', 'Nurse Practitioner', 'Physician Assistant', 'Urgent Care Nurse']
};

// Medical conditions by specialty
const MEDICAL_CONDITIONS = {
  'Family Medicine': ['Hypertension', 'Type 2 Diabetes', 'Upper Respiratory Infection', 'Annual Physical', 'Anxiety'],
  'Cardiology': ['Coronary Artery Disease', 'Heart Failure', 'Atrial Fibrillation', 'Hypertension', 'Chest Pain'],
  'Pediatrics': ['Well Child Visit', 'Asthma', 'ADHD', 'Ear Infection', 'Developmental Delay'],
  'Orthopedics': ['Knee Pain', 'Back Pain', 'Fracture', 'Arthritis', 'Sports Injury'],
  'Dermatology': ['Acne', 'Skin Cancer Screening', 'Eczema', 'Psoriasis', 'Mole Removal'],
  'Neurology': ['Migraine', 'Epilepsy', 'Multiple Sclerosis', 'Stroke', 'Memory Loss'],
  'Emergency Medicine': ['Chest Pain', 'Abdominal Pain', 'Trauma', 'Shortness of Breath', 'Allergic Reaction'],
  'Internal Medicine': ['Diabetes Management', 'Hypertension', 'Chronic Kidney Disease', 'COPD', 'Preventive Care'],
  'Obstetrics': ['Pregnancy Care', 'Annual Exam', 'Contraception', 'Menopause', 'Fertility Consultation'],
  'Urgent Care': ['Minor Injury', 'Cold/Flu', 'UTI', 'Strep Throat', 'Minor Burns']
};

// Common medications by condition
const MEDICATIONS = {
  'Hypertension': ['Lisinopril', 'Amlodipine', 'Metoprolol', 'Hydrochlorothiazide'],
  'Type 2 Diabetes': ['Metformin', 'Insulin', 'Glipizide', 'Januvia'],
  'Asthma': ['Albuterol', 'Fluticasone', 'Montelukast', 'Prednisone'],
  'Depression': ['Sertraline', 'Fluoxetine', 'Escitalopram', 'Bupropion'],
  'Anxiety': ['Lorazepam', 'Alprazolam', 'Buspirone', 'Hydroxyzine'],
  'Pain': ['Ibuprofen', 'Acetaminophen', 'Naproxen', 'Tramadol'],
  'Infection': ['Amoxicillin', 'Azithromycin', 'Cephalexin', 'Doxycycline']
};

// Lab test types
const LAB_TESTS = [
  'Complete Blood Count (CBC)',
  'Basic Metabolic Panel (BMP)',
  'Lipid Panel',
  'Thyroid Function Tests',
  'Hemoglobin A1C',
  'Liver Function Tests',
  'Urinalysis',
  'Vitamin D Level',
  'PSA',
  'Mammogram',
  'Colonoscopy',
  'EKG',
  'Chest X-Ray',
  'MRI',
  'CT Scan',
  'Ultrasound'
];

// Immunization types
const IMMUNIZATIONS = [
  'COVID-19 Vaccine',
  'Influenza Vaccine',
  'Tdap (Tetanus, Diphtheria, Pertussis)',
  'MMR (Measles, Mumps, Rubella)',
  'Hepatitis B',
  'Pneumococcal',
  'Shingles (Zoster)',
  'HPV Vaccine',
  'Meningococcal',
  'Varicella (Chickenpox)'
];

/**
 * Get organization specialty based on name
 */
function getOrganizationSpecialty(orgName) {
  if (orgName.includes('Cardiology')) return 'Cardiology';
  if (orgName.includes('Pediatric') || orgName.includes('Pediatrics')) return 'Pediatrics';
  if (orgName.includes('Orthopedic')) return 'Orthopedics';
  if (orgName.includes('Dermatology')) return 'Dermatology';
  if (orgName.includes('Neurology')) return 'Neurology';
  if (orgName.includes('Urgent Care')) return 'Urgent Care';
  if (orgName.includes('OB/GYN')) return 'Obstetrics';
  if (orgName.includes('Hospital')) return 'Emergency Medicine';
  return 'Family Medicine';
}

/**
 * Generate realistic provider data for an organization
 */
function generateProviders(orgId, orgName, count = 5) {
  const specialty = getOrganizationSpecialty(orgName);
  const specialtyRoles = MEDICAL_SPECIALTIES[specialty] || MEDICAL_SPECIALTIES['Family Medicine'];

  const providers = [];

  for (let i = 0; i < count; i++) {
    const sex = faker.person.sexType();
    const firstName = faker.person.firstName(sex);
    const lastName = faker.person.lastName();
    const roleTitle = faker.helpers.arrayElement(specialtyRoles);

    // Map role titles to actual user_role enum values
    let userRole = 'physician';
    let providerType = 'doctor';

    if (roleTitle.includes('Nurse')) {
      userRole = 'registered_nurse';
      providerType = 'nurse';
    } else if (roleTitle.includes('Assistant')) {
      userRole = 'medical_assistant';
      providerType = 'admin';
    } else if (roleTitle.includes('Specialist')) {
      userRole = 'physician';
      providerType = 'specialist';
    }

    providers.push({
      id: faker.string.uuid(),
      user_id: null, // Not linking to auth users in this seed data
      organization_id: orgId,
      department_id: null, // We don't have departments in our seed data
      first_name: firstName,
      last_name: lastName,
      provider_type: providerType,
      specialization: specialty,
      license_number: faker.string.alphanumeric(8).toUpperCase(),
      role: userRole,
      specialties: [specialty.toLowerCase().replace(' ', '_')],
      credentials: `{"degree": "MD", "board_certified": ${faker.datatype.boolean(0.9)}, "years_experience": ${faker.number.int({ min: 1, max: 30 })}}`,
      schedule_settings: '{"default_hours": "9-17", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}',
      permissions: '{}',
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return providers;
}

/**
 * Generate realistic patient data for an organization
 */
function generatePatients(orgId, count = 50) {
  const patients = [];

  for (let i = 0; i < count; i++) {
    const sex = faker.person.sexType();
    const firstName = faker.person.firstName(sex);
    const lastName = faker.person.lastName();
    const birthDate = faker.date.birthdate({ min: 0, max: 90, mode: 'age' });

    patients.push({
      id: faker.string.uuid(),
      organization_id: orgId,
      first_name: firstName,
      last_name: lastName,
      email: faker.internet.email({ firstName, lastName }),
      phone: faker.phone.number(),
      date_of_birth: birthDate.toISOString().split('T')[0],
      gender: sex,
      address: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state({ abbreviated: true }),
      zip_code: faker.location.zipCode(),
      emergency_contact_name: faker.person.fullName(),
      emergency_contact_phone: faker.phone.number(),
      insurance_provider: faker.helpers.arrayElement(['Blue Cross Blue Shield', 'Aetna', 'Cigna', 'UnitedHealth', 'Kaiser Permanente']),
      insurance_policy_number: faker.string.alphanumeric(12).toUpperCase(),
      active: faker.datatype.boolean(0.95),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return patients;
}

/**
 * Generate comprehensive medical records
 */
function generateMedicalRecords(patients, providers, orgId, count = 100) {
  const records = [];
  const specialty = getOrganizationSpecialty(''); // Will default to Family Medicine
  const conditions = MEDICAL_CONDITIONS[specialty] || MEDICAL_CONDITIONS['Family Medicine'];

  for (let i = 0; i < count; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const provider = faker.helpers.arrayElement(providers);
    const condition = faker.helpers.arrayElement(conditions);

    records.push({
      id: faker.string.uuid(),
      organization_id: orgId,
      patient_id: patient.id,
      provider_id: provider.id,
      visit_date: faker.date.past({ years: 2 }),
      chief_complaint: `${condition} follow-up`,
      diagnosis: [condition, faker.helpers.maybe(() => faker.helpers.arrayElement(conditions), { probability: 0.3 })].filter(Boolean),
      treatment_plan: `Continue current treatment plan for ${condition}. Follow up in ${faker.number.int({ min: 1, max: 6 })} months.`,
      notes: faker.lorem.paragraph(),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return records;
}

/**
 * Generate appointments for patients and providers
 */
function generateAppointments(patients, providers, orgId, count = 200) {
  const appointments = [];
  const appointmentTypes = ['New Patient', 'Follow-up', 'Annual Physical', 'Consultation', 'Procedure', 'Emergency'];

  for (let i = 0; i < count; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const provider = faker.helpers.arrayElement(providers);
    const appointmentDate = faker.date.between({
      from: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // 1 year ago
      to: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)     // 3 months future
    });

    appointments.push({
      id: faker.string.uuid(),
      organization_id: orgId,
      patient_id: patient.id,
      provider_id: provider.id,
      appointment_date: appointmentDate,
      appointment_type: faker.helpers.arrayElement(appointmentTypes),
      status: faker.helpers.weightedArrayElement([
        { weight: 0.7, value: 'completed' },
        { weight: 0.2, value: 'scheduled' },
        { weight: 0.07, value: 'cancelled' },
        { weight: 0.03, value: 'no-show' }
      ]),
      duration_minutes: faker.helpers.arrayElement([15, 30, 45, 60]),
      notes: faker.lorem.sentence(),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return appointments;
}

/**
 * Generate medications for patients
 */
function generateMedications(patients, providers, count = 150) {
  const medications = [];

  for (let i = 0; i < count; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const provider = faker.helpers.arrayElement(providers);
    const conditionType = faker.helpers.arrayElement(Object.keys(MEDICATIONS));
    const medication = faker.helpers.arrayElement(MEDICATIONS[conditionType]);

    medications.push({
      id: faker.string.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      medication_name: medication,
      dosage: `${faker.number.int({ min: 5, max: 100 })}mg`,
      frequency: faker.helpers.arrayElement(['Once daily', 'Twice daily', 'Three times daily', 'As needed']),
      start_date: faker.date.past({ years: 1 }),
      end_date: faker.helpers.maybe(() => faker.date.future({ years: 1 }), { probability: 0.3 }),
      active: faker.datatype.boolean(0.8),
      instructions: `Take ${medication} as prescribed. ${faker.lorem.sentence()}`,
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return medications;
}

/**
 * Generate lab results
 */
function generateLabResults(patients, providers, count = 100) {
  const labResults = [];

  for (let i = 0; i < count; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const provider = faker.helpers.arrayElement(providers);
    const testType = faker.helpers.arrayElement(LAB_TESTS);

    labResults.push({
      id: faker.string.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      test_name: testType,
      test_date: faker.date.past({ years: 1 }),
      results: faker.lorem.paragraph(),
      normal_range: faker.helpers.arrayElement(['Normal', 'Abnormal', 'Critical']),
      status: faker.helpers.arrayElement(['completed', 'pending', 'in_progress']),
      notes: faker.lorem.sentence(),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return labResults;
}

/**
 * Generate immunizations
 */
function generateImmunizations(patients, providers, count = 80) {
  const immunizations = [];

  for (let i = 0; i < count; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const provider = faker.helpers.arrayElement(providers);
    const vaccine = faker.helpers.arrayElement(IMMUNIZATIONS);

    immunizations.push({
      id: faker.string.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      vaccine_name: vaccine,
      administration_date: faker.date.past({ years: 2 }),
      lot_number: faker.string.alphanumeric(8).toUpperCase(),
      manufacturer: faker.helpers.arrayElement(['Pfizer', 'Moderna', 'Johnson & Johnson', 'GSK', 'Merck']),
      site: faker.helpers.arrayElement(['Left arm', 'Right arm', 'Left thigh', 'Right thigh']),
      route: faker.helpers.arrayElement(['Intramuscular', 'Subcutaneous', 'Oral']),
      dose: `${faker.number.float({ min: 0.1, max: 2.0, fractionDigits: 1 })}mL`,
      next_due_date: faker.helpers.maybe(() => faker.date.future({ years: 1 }), { probability: 0.6 }),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return immunizations;
}

/**
 * Generate tasks for workflow management
 */
function generateTasks(patients, providers, orgId, count = 100) {
  const tasks = [];
  const taskTypes = ['Follow-up call', 'Lab review', 'Prescription renewal', 'Insurance authorization', 'Referral processing'];
  const priorities = ['low', 'medium', 'high', 'urgent'];

  for (let i = 0; i < count; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const assignedTo = faker.helpers.arrayElement(providers);
    const assignedBy = faker.helpers.arrayElement(providers);
    const taskType = faker.helpers.arrayElement(taskTypes);

    // Weighted status selection
    const status = faker.helpers.weightedArrayElement([
      { weight: 0.4, value: 'pending' },
      { weight: 0.3, value: 'in_progress' },
      { weight: 0.25, value: 'completed' },
      { weight: 0.05, value: 'cancelled' }
    ]);

    tasks.push({
      id: faker.string.uuid(),
      organization_id: orgId,
      department_id: null, // We don't have departments in our seed data
      title: `${taskType} for ${patient.first_name} ${patient.last_name}`,
      description: faker.lorem.paragraph(),
      priority: faker.helpers.arrayElement(priorities),
      status: status,
      assigned_to: assignedTo.id,
      assigned_by: assignedBy.id,
      due_date: faker.date.future({ days: 30 }),
      completed_at: status === 'completed' ? faker.date.past({ days: 7 }) : null,
      related_to: `{"patient_id": "${patient.id}", "type": "patient_task"}`,
      metadata: '{}',
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return tasks;
}

/**
 * Generate additional comprehensive data (claims, patient alerts)
 */
function generateAdditionalData(patients, providers) {
  // Generate claims
  const claims = [];
  for (let i = 0; i < 50; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const provider = faker.helpers.arrayElement(providers);

    claims.push({
      id: faker.string.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      claim_number: faker.string.alphanumeric(10).toUpperCase(),
      service_date: faker.date.past({ years: 1 }),
      diagnosis_codes: [faker.string.alphanumeric(5).toUpperCase()],
      procedure_codes: [faker.string.alphanumeric(5).toUpperCase()],
      amount_billed: faker.number.float({ min: 100, max: 5000, fractionDigits: 2 }),
      amount_paid: faker.number.float({ min: 50, max: 4000, fractionDigits: 2 }),
      status: faker.helpers.arrayElement(['submitted', 'processing', 'paid', 'denied']),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  // Generate patient alerts
  const alerts = [];
  for (let i = 0; i < 30; i++) {
    const patient = faker.helpers.arrayElement(patients);
    const provider = faker.helpers.arrayElement(providers);

    alerts.push({
      id: faker.string.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      alert_type: faker.helpers.arrayElement(['allergy', 'medication_interaction', 'critical_value', 'follow_up_needed']),
      message: faker.lorem.sentence(),
      severity: faker.helpers.arrayElement(['low', 'medium', 'high', 'critical']),
      active: faker.datatype.boolean(0.8),
      acknowledged: faker.datatype.boolean(0.6),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return { claims, alerts };
}

/**
 * Convert data to SQL INSERT statements
 */
function generateSQLInserts(tableName, data) {
  if (!data || data.length === 0) return '';

  const columns = Object.keys(data[0]);
  const values = data.map(row => {
    const rowValues = columns.map(col => {
      const value = row[col];
      if (value === null || value === undefined) return 'NULL';
      if (value === 'NOW()') return 'NOW()';
      if (typeof value === 'string') {
        // Check if it's a JSON string (starts with { or [)
        if ((value.startsWith('{') && value.endsWith('}')) || (value.startsWith('[') && value.endsWith(']'))) {
          return `'${value.replace(/'/g, "''")}'::jsonb`;
        }
        return `'${value.replace(/'/g, "''")}'`;
      }
      if (typeof value === 'boolean') return value;
      if (Array.isArray(value)) return `ARRAY[${value.map(v => `'${v}'`).join(', ')}]`;
      if (value instanceof Date) return `'${value.toISOString()}'`;
      return value;
    });
    return `(${rowValues.join(', ')})`;
  });

  return `
-- Insert ${tableName}
INSERT INTO public.${tableName} (${columns.join(', ')})
VALUES ${values.join(',\n       ')};
`;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting comprehensive seed data generation...');
  console.log('📊 This will create extensive realistic medical data for all organizations');

  // First, get existing organizations
  const organizations = [
    { id: '1', name: 'Spritely Medical Center' },
    { id: '2', name: 'Spritely Community Clinic' },
    { id: '3', name: 'Spritely Pediatrics' },
    { id: '4', name: 'Dermatology Partners' },
    { id: '5', name: 'Downtown Urgent Care' },
    { id: '6', name: 'Eastside Community Hospital' },
    { id: '7', name: 'Harbor View Hospital' },
    { id: '8', name: 'Lakeside OB/GYN' },
    { id: '9', name: 'Metropolitan Medical Center' },
    { id: '10', name: 'Neurology Consultants' },
    { id: '11', name: 'Northside Regional Hospital' },
    { id: '12', name: 'Parkside Family Medicine' },
    { id: '13', name: 'Riverside Health Center' },
    { id: '14', name: 'Summit Orthopedic Specialists' },
    { id: '15', name: 'Valley Cardiology Associates' },
    { id: '16', name: 'Westlake Medical System' }
  ];

  let allSQL = `-- Comprehensive Seed Data Generated by Faker.js
-- Generated on: ${new Date().toISOString()}
-- This file contains extensive realistic medical data for all organizations

BEGIN;

-- Clear existing comprehensive data (keep core reference data)
-- Delete in order to respect foreign key constraints (child tables first)
DELETE FROM public.task_comments;
DELETE FROM public.task_watchers;
DELETE FROM public.clinical_notes;
DELETE FROM public.lab_results;
DELETE FROM public.immunizations;
DELETE FROM public.medications;
DELETE FROM public.claims;
DELETE FROM public.patient_alerts;
DELETE FROM public.tasks;
DELETE FROM public.appointments;
DELETE FROM public.medical_records;
DELETE FROM public.allergies;
DELETE FROM public.vital_signs;
DELETE FROM public.orders;
DELETE FROM public.referrals;
DELETE FROM public.patient_education_records;
DELETE FROM public.patient_portal_settings;
DELETE FROM public.patient_questionnaires;
DELETE FROM public.care_team_members;
DELETE FROM public.conversation_participants;
DELETE FROM public.messages;
DELETE FROM public.message_states;
DELETE FROM public.conversations;
DELETE FROM public.notifications;
DELETE FROM public.notification_preferences;
DELETE FROM public.activity_logs;
DELETE FROM public.analytics_events;
DELETE FROM public.analytics_metrics;
DELETE FROM public.audit_logs;
DELETE FROM public.inventory_transactions;
DELETE FROM public.inventory_items;
DELETE FROM public.workflow_instances;
DELETE FROM public.workflow_logs;
DELETE FROM public.templates;
DELETE FROM public.documents;
DELETE FROM public.healthcare_providers;
DELETE FROM public.patients;
DELETE FROM public.teams;
DELETE FROM public.facilities;
DELETE FROM public.departments;
DELETE FROM public.workflows;
DELETE FROM public.organization_invites;
DELETE FROM public.user_roles WHERE organization_id NOT IN (SELECT id FROM organizations WHERE name LIKE 'Spritely%');

`;

  // Generate data for each organization
  for (const org of organizations) {
    console.log(`\n📋 Generating data for: ${org.name}`);

    // Determine data volume based on organization type
    const isMainOrg = org.name.includes('Spritely');
    const patientCount = isMainOrg ? 80 : faker.number.int({ min: 30, max: 60 });
    const providerCount = isMainOrg ? 8 : faker.number.int({ min: 3, max: 6 });

    console.log(`   👥 Generating ${patientCount} patients and ${providerCount} providers`);

    // Generate core data
    const providers = generateProviders(org.id, org.name, providerCount);
    const patients = generatePatients(org.id, patientCount);
    const medicalRecords = generateMedicalRecords(patients, providers, org.id, Math.floor(patientCount * 0.4));
    const appointments = generateAppointments(patients, providers, org.id, Math.floor(patientCount * 2.5));
    const medications = generateMedications(patients, providers, Math.floor(patientCount * 0.8));
    const labResults = generateLabResults(patients, providers, Math.floor(patientCount * 0.6));
    const immunizations = generateImmunizations(patients, providers, Math.floor(patientCount * 0.5));
    const tasks = generateTasks(patients, providers, org.id, Math.floor(patientCount * 0.7));
    const { claims, alerts } = generateAdditionalData(patients, providers, org.id);

    console.log(`   📊 Generated: ${appointments.length} appointments, ${medicalRecords.length} medical records, ${medications.length} medications`);
    console.log(`   🧪 Generated: ${labResults.length} lab results, ${immunizations.length} immunizations, ${tasks.length} tasks`);

    // Add to SQL
    allSQL += `\n-- Data for ${org.name}\n`;
    allSQL += generateSQLInserts('healthcare_providers', providers);
    allSQL += generateSQLInserts('patients', patients);
    allSQL += generateSQLInserts('medical_records', medicalRecords);
    allSQL += generateSQLInserts('appointments', appointments);
    allSQL += generateSQLInserts('medications', medications);
    allSQL += generateSQLInserts('lab_results', labResults);
    allSQL += generateSQLInserts('immunizations', immunizations);
    allSQL += generateSQLInserts('tasks', tasks);
    allSQL += generateSQLInserts('claims', claims);
    allSQL += generateSQLInserts('patient_alerts', alerts);
  }

  allSQL += '\nCOMMIT;\n';

  // Write to file
  const outputPath = `${process.cwd()}/supabase/seeds/environments/development/99_comprehensive_data.sql`;
  fs.writeFileSync(outputPath, allSQL);

  console.log('\n✅ Comprehensive seed data generation complete!');
  console.log(`📁 Generated file: ${outputPath}`);
  console.log('🔄 Run "npx supabase db reset --local" to apply the new seed data');
}

// Run the script
main().catch(console.error);
