#!/usr/bin/env node

/**
 * Simple Comprehensive Seed Data Generator for Spritely Medical Application
 *
 * This script generates extensive, realistic medical data for all organizations
 * using Node.js built-in modules to create a robust demo environment.
 */

import crypto from 'crypto';
import fs from 'fs';

// Simple random data generator using Node.js built-ins
class SimpleDataGenerator {
  constructor(seed = 12345) {
    this.seed = seed;
    this.counter = 0;
  }

  // Simple seeded random number generator
  random() {
    const x = Math.sin(this.seed + this.counter++) * 10000;
    return x - Math.floor(x);
  }

  // Generate random integer between min and max
  int(min, max) {
    return Math.floor(this.random() * (max - min + 1)) + min;
  }

  // Generate random float
  float(min, max, decimals = 2) {
    const value = this.random() * (max - min) + min;
    return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  // Pick random element from array
  arrayElement(array) {
    return array[Math.floor(this.random() * array.length)];
  }

  // Generate UUID
  uuid() {
    return crypto.randomUUID();
  }

  // Generate random string
  alphanumeric(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(this.random() * chars.length));
    }
    return result;
  }

  // Generate numeric string
  numeric(length) {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += Math.floor(this.random() * 10);
    }
    return result;
  }

  // Generate phone number
  phone() {
    return `555-${this.numeric(3)}-${this.numeric(4)}`;
  }

  // Generate email
  email(firstName, lastName) {
    const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'example.com'];
    const domain = this.arrayElement(domains);
    return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`;
  }

  // Generate date in the past
  pastDate(years = 1) {
    const now = new Date();
    const pastTime = now.getTime() - (this.random() * years * 365 * 24 * 60 * 60 * 1000);
    return new Date(pastTime);
  }

  // Generate future date
  futureDate(days = 30) {
    const now = new Date();
    const futureTime = now.getTime() + (this.random() * days * 24 * 60 * 60 * 1000);
    return new Date(futureTime);
  }

  // Generate birthdate
  birthdate(minAge = 0, maxAge = 90) {
    const now = new Date();
    const age = this.int(minAge, maxAge);
    const birthYear = now.getFullYear() - age;
    const birthMonth = this.int(0, 11);
    const birthDay = this.int(1, 28); // Safe day for all months
    return new Date(birthYear, birthMonth, birthDay);
  }

  // Generate boolean with probability
  boolean(probability = 0.5) {
    return this.random() < probability;
  }

  // Generate paragraph
  paragraph() {
    const sentences = [
      'Patient presents with typical symptoms.',
      'Examination reveals normal findings.',
      'Treatment plan has been effective.',
      'Follow-up recommended in appropriate timeframe.',
      'Patient education provided regarding condition.',
      'Medication compliance is important for recovery.',
      'Lifestyle modifications discussed with patient.',
      'No adverse reactions reported to current treatment.'
    ];
    const numSentences = this.int(2, 4);
    let result = '';
    for (let i = 0; i < numSentences; i++) {
      result += this.arrayElement(sentences) + ' ';
    }
    return result.trim();
  }

  // Generate sentence
  sentence() {
    const words = [
      'Patient', 'reports', 'improvement', 'symptoms', 'condition', 'treatment',
      'medication', 'effective', 'stable', 'normal', 'follow-up', 'recommended',
      'continue', 'current', 'plan', 'monitoring', 'required', 'assessment'
    ];
    const numWords = this.int(5, 12);
    let result = '';
    for (let i = 0; i < numWords; i++) {
      result += this.arrayElement(words) + ' ';
    }
    return result.trim().charAt(0).toUpperCase() + result.trim().slice(1) + '.';
  }

  // Generate names
  firstName(sex) {
    const maleNames = ['James', 'John', 'Robert', 'Michael', 'William', 'David', 'Richard', 'Joseph', 'Thomas', 'Christopher'];
    const femaleNames = ['Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Barbara', 'Susan', 'Jessica', 'Sarah', 'Karen'];
    return sex === 'male' ? this.arrayElement(maleNames) : this.arrayElement(femaleNames);
  }

  lastName() {
    const names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
    return this.arrayElement(names);
  }

  fullName() {
    const sex = this.arrayElement(['male', 'female']);
    return `${this.firstName(sex)} ${this.lastName()}`;
  }

  sexType() {
    return this.arrayElement(['male', 'female']);
  }

  // Generate addresses
  streetAddress() {
    const numbers = this.int(100, 9999);
    const streets = ['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr', 'Maple Ln', 'Cedar Blvd', 'Park Ave', 'First St'];
    return `${numbers} ${this.arrayElement(streets)}`;
  }

  city() {
    const cities = ['Springfield', 'Franklin', 'Georgetown', 'Madison', 'Washington', 'Arlington', 'Centerville', 'Salem'];
    return this.arrayElement(cities);
  }

  state() {
    const states = ['CA', 'TX', 'FL', 'NY', 'PA', 'IL', 'OH', 'GA', 'NC', 'MI'];
    return this.arrayElement(states);
  }

  zipCode() {
    return this.numeric(5);
  }

  // Generate company names
  companyName() {
    const prefixes = ['Advanced', 'Premier', 'Elite', 'Superior', 'Excellence', 'Quality', 'Professional'];
    const suffixes = ['Medical', 'Healthcare', 'Clinic', 'Center', 'Institute', 'Associates', 'Group'];
    return `${this.arrayElement(prefixes)} ${this.arrayElement(suffixes)}`;
  }
}

const faker = new SimpleDataGenerator(12345);

// Medical specialties and their corresponding organization types
const MEDICAL_SPECIALTIES = {
  'Family Medicine': ['General Practitioner', 'Family Medicine Physician', 'Nurse Practitioner', 'Physician Assistant'],
  'Cardiology': ['Cardiologist', 'Cardiac Surgeon', 'Interventional Cardiologist', 'Cardiac Nurse'],
  'Pediatrics': ['Pediatrician', 'Pediatric Nurse', 'Child Psychologist', 'Pediatric Specialist'],
  'Orthopedics': ['Orthopedic Surgeon', 'Sports Medicine Physician', 'Physical Therapist', 'Orthopedic Nurse'],
  'Dermatology': ['Dermatologist', 'Cosmetic Surgeon', 'Dermatology Nurse', 'Mohs Surgeon'],
  'Neurology': ['Neurologist', 'Neurosurgeon', 'Neuropsychologist', 'Neuro Nurse'],
  'Emergency Medicine': ['Emergency Physician', 'Trauma Surgeon', 'Emergency Nurse', 'Paramedic'],
  'Internal Medicine': ['Internist', 'Hospitalist', 'Internal Medicine Nurse', 'Clinical Specialist'],
  'Obstetrics': ['Obstetrician', 'Gynecologist', 'Midwife', 'Women\'s Health Nurse'],
  'Urgent Care': ['Urgent Care Physician', 'Nurse Practitioner', 'Physician Assistant', 'Urgent Care Nurse']
};

// Medical conditions by specialty
const MEDICAL_CONDITIONS = {
  'Family Medicine': ['Hypertension', 'Type 2 Diabetes', 'Upper Respiratory Infection', 'Annual Physical', 'Anxiety'],
  'Cardiology': ['Coronary Artery Disease', 'Heart Failure', 'Atrial Fibrillation', 'Hypertension', 'Chest Pain'],
  'Pediatrics': ['Well Child Visit', 'Asthma', 'ADHD', 'Ear Infection', 'Developmental Delay'],
  'Orthopedics': ['Knee Pain', 'Back Pain', 'Fracture', 'Arthritis', 'Sports Injury'],
  'Dermatology': ['Acne', 'Skin Cancer Screening', 'Eczema', 'Psoriasis', 'Mole Removal'],
  'Neurology': ['Migraine', 'Epilepsy', 'Multiple Sclerosis', 'Stroke', 'Memory Loss'],
  'Emergency Medicine': ['Chest Pain', 'Abdominal Pain', 'Trauma', 'Shortness of Breath', 'Allergic Reaction'],
  'Internal Medicine': ['Diabetes Management', 'Hypertension', 'Chronic Kidney Disease', 'COPD', 'Preventive Care'],
  'Obstetrics': ['Pregnancy Care', 'Annual Exam', 'Contraception', 'Menopause', 'Fertility Consultation'],
  'Urgent Care': ['Minor Injury', 'Cold/Flu', 'UTI', 'Strep Throat', 'Minor Burns']
};

// Common medications by condition
const MEDICATIONS = {
  'Hypertension': ['Lisinopril', 'Amlodipine', 'Metoprolol', 'Hydrochlorothiazide'],
  'Type 2 Diabetes': ['Metformin', 'Insulin', 'Glipizide', 'Januvia'],
  'Asthma': ['Albuterol', 'Fluticasone', 'Montelukast', 'Prednisone'],
  'Depression': ['Sertraline', 'Fluoxetine', 'Escitalopram', 'Bupropion'],
  'Anxiety': ['Lorazepam', 'Alprazolam', 'Buspirone', 'Hydroxyzine'],
  'Pain': ['Ibuprofen', 'Acetaminophen', 'Naproxen', 'Tramadol'],
  'Infection': ['Amoxicillin', 'Azithromycin', 'Cephalexin', 'Doxycycline']
};

// Lab test types
const LAB_TESTS = [
  'Complete Blood Count (CBC)', 'Basic Metabolic Panel (BMP)', 'Lipid Panel', 'Thyroid Function Tests',
  'Hemoglobin A1C', 'Liver Function Tests', 'Urinalysis', 'Vitamin D Level', 'PSA', 'Mammogram',
  'Colonoscopy', 'EKG', 'Chest X-Ray', 'MRI', 'CT Scan', 'Ultrasound'
];

// Immunization types
const IMMUNIZATIONS = [
  'COVID-19 Vaccine', 'Influenza Vaccine', 'Tdap (Tetanus, Diphtheria, Pertussis)', 'MMR (Measles, Mumps, Rubella)',
  'Hepatitis B', 'Pneumococcal', 'Shingles (Zoster)', 'HPV Vaccine', 'Meningococcal', 'Varicella (Chickenpox)'
];

/**
 * Get organization specialty based on name
 */
function getOrganizationSpecialty(orgName) {
  if (orgName.includes('Cardiology')) return 'Cardiology';
  if (orgName.includes('Pediatric') || orgName.includes('Pediatrics')) return 'Pediatrics';
  if (orgName.includes('Orthopedic')) return 'Orthopedics';
  if (orgName.includes('Dermatology')) return 'Dermatology';
  if (orgName.includes('Neurology')) return 'Neurology';
  if (orgName.includes('Urgent Care')) return 'Urgent Care';
  if (orgName.includes('OB/GYN')) return 'Obstetrics';
  if (orgName.includes('Hospital')) return 'Emergency Medicine';
  return 'Family Medicine';
}

/**
 * Generate realistic provider data for an organization
 */
function generateProviders(orgId, orgName, count = 5) {
  const specialty = getOrganizationSpecialty(orgName);
  const specialtyRoles = MEDICAL_SPECIALTIES[specialty] || MEDICAL_SPECIALTIES['Family Medicine'];

  const providers = [];

  for (let i = 0; i < count; i++) {
    const sex = faker.sexType();
    const firstName = faker.firstName(sex);
    const lastName = faker.lastName();
    const role = faker.arrayElement(specialtyRoles);

    providers.push({
      id: faker.uuid(),
      organization_id: orgId,
      first_name: firstName,
      last_name: lastName,
      email: faker.email(firstName, lastName),
      phone: faker.phone(),
      specialty: specialty,
      role: role,
      license_number: faker.alphanumeric(8).toUpperCase(),
      npi_number: faker.numeric(10),
      years_experience: faker.int(1, 30),
      education: `${faker.companyName()} Medical School`,
      board_certified: faker.boolean(0.9),
      active: true,
      hire_date: faker.pastDate(10),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return providers;
}

/**
 * Generate realistic patient data for an organization
 */
function generatePatients(orgId, count = 50) {
  const patients = [];

  for (let i = 0; i < count; i++) {
    const sex = faker.sexType();
    const firstName = faker.firstName(sex);
    const lastName = faker.lastName();
    const birthDate = faker.birthdate(0, 90);

    patients.push({
      id: faker.uuid(),
      organization_id: orgId,
      first_name: firstName,
      last_name: lastName,
      email: faker.email(firstName, lastName),
      phone: faker.phone(),
      date_of_birth: birthDate.toISOString().split('T')[0],
      gender: sex,
      address: faker.streetAddress(),
      city: faker.city(),
      state: faker.state(),
      zip_code: faker.zipCode(),
      emergency_contact_name: faker.fullName(),
      emergency_contact_phone: faker.phone(),
      insurance_provider: faker.arrayElement(['Blue Cross Blue Shield', 'Aetna', 'Cigna', 'UnitedHealth', 'Kaiser Permanente']),
      insurance_policy_number: faker.alphanumeric(12).toUpperCase(),
      active: faker.boolean(0.95),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return patients;
}

/**
 * Generate comprehensive medical records
 */
function generateMedicalRecords(patients, providers, orgId, count = 100) {
  const records = [];
  const specialty = getOrganizationSpecialty(''); // Will default to Family Medicine
  const conditions = MEDICAL_CONDITIONS[specialty] || MEDICAL_CONDITIONS['Family Medicine'];

  for (let i = 0; i < count; i++) {
    const patient = faker.arrayElement(patients);
    const provider = faker.arrayElement(providers);
    const condition = faker.arrayElement(conditions);

    records.push({
      id: faker.uuid(),
      organization_id: orgId,
      patient_id: patient.id,
      provider_id: provider.id,
      visit_date: faker.pastDate(2),
      chief_complaint: `${condition} follow-up`,
      diagnosis: [condition],
      treatment_plan: `Continue current treatment plan for ${condition}. Follow up in ${faker.int(1, 6)} months.`,
      notes: faker.paragraph(),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return records;
}

/**
 * Generate appointments for patients and providers
 */
function generateAppointments(patients, providers, orgId, count = 200) {
  const appointments = [];
  const appointmentTypes = ['New Patient', 'Follow-up', 'Annual Physical', 'Consultation', 'Procedure', 'Emergency'];
  const statuses = [
    { weight: 0.7, value: 'completed' },
    { weight: 0.2, value: 'scheduled' },
    { weight: 0.07, value: 'cancelled' },
    { weight: 0.03, value: 'no-show' }
  ];

  for (let i = 0; i < count; i++) {
    const patient = faker.arrayElement(patients);
    const provider = faker.arrayElement(providers);

    // Generate date between 1 year ago and 3 months future
    const now = new Date();
    const pastTime = now.getTime() - (365 * 24 * 60 * 60 * 1000);
    const futureTime = now.getTime() + (90 * 24 * 60 * 60 * 1000);
    const appointmentTime = pastTime + (faker.random() * (futureTime - pastTime));
    const appointmentDate = new Date(appointmentTime);

    // Weighted status selection
    const rand = faker.random();
    let cumulativeWeight = 0;
    let status = 'completed';
    for (const statusOption of statuses) {
      cumulativeWeight += statusOption.weight;
      if (rand <= cumulativeWeight) {
        status = statusOption.value;
        break;
      }
    }

    appointments.push({
      id: faker.uuid(),
      organization_id: orgId,
      patient_id: patient.id,
      provider_id: provider.id,
      appointment_date: appointmentDate,
      appointment_type: faker.arrayElement(appointmentTypes),
      status: status,
      duration_minutes: faker.arrayElement([15, 30, 45, 60]),
      notes: faker.sentence(),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return appointments;
}

/**
 * Generate medications for patients
 */
function generateMedications(patients, providers, count = 150) {
  const medications = [];

  for (let i = 0; i < count; i++) {
    const patient = faker.arrayElement(patients);
    const provider = faker.arrayElement(providers);
    const conditionType = faker.arrayElement(Object.keys(MEDICATIONS));
    const medication = faker.arrayElement(MEDICATIONS[conditionType]);

    medications.push({
      id: faker.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      medication_name: medication,
      dosage: `${faker.int(5, 100)}mg`,
      frequency: faker.arrayElement(['Once daily', 'Twice daily', 'Three times daily', 'As needed']),
      start_date: faker.pastDate(1),
      end_date: faker.boolean(0.3) ? faker.futureDate(365) : null,
      active: faker.boolean(0.8),
      instructions: `Take ${medication} as prescribed. ${faker.sentence()}`,
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return medications;
}

/**
 * Generate lab results
 */
function generateLabResults(patients, providers, count = 100) {
  const labResults = [];

  for (let i = 0; i < count; i++) {
    const patient = faker.arrayElement(patients);
    const provider = faker.arrayElement(providers);
    const testType = faker.arrayElement(LAB_TESTS);

    labResults.push({
      id: faker.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      test_name: testType,
      test_date: faker.pastDate(1),
      results: faker.paragraph(),
      normal_range: faker.arrayElement(['Normal', 'Abnormal', 'Critical']),
      status: faker.arrayElement(['completed', 'pending', 'in_progress']),
      notes: faker.sentence(),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return labResults;
}

/**
 * Generate immunizations
 */
function generateImmunizations(patients, providers, count = 80) {
  const immunizations = [];

  for (let i = 0; i < count; i++) {
    const patient = faker.arrayElement(patients);
    const provider = faker.arrayElement(providers);
    const vaccine = faker.arrayElement(IMMUNIZATIONS);

    immunizations.push({
      id: faker.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      vaccine_name: vaccine,
      administration_date: faker.pastDate(2),
      lot_number: faker.alphanumeric(8).toUpperCase(),
      manufacturer: faker.arrayElement(['Pfizer', 'Moderna', 'Johnson & Johnson', 'GSK', 'Merck']),
      site: faker.arrayElement(['Left arm', 'Right arm', 'Left thigh', 'Right thigh']),
      route: faker.arrayElement(['Intramuscular', 'Subcutaneous', 'Oral']),
      dose: `${faker.float(0.1, 2.0, 1)}mL`,
      next_due_date: faker.boolean(0.6) ? faker.futureDate(365) : null,
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return immunizations;
}

/**
 * Generate tasks for workflow management
 */
function generateTasks(patients, providers, orgId, count = 100) {
  const tasks = [];
  const taskTypes = ['Follow-up call', 'Lab review', 'Prescription renewal', 'Insurance authorization', 'Referral processing'];
  const priorities = ['low', 'medium', 'high', 'urgent'];
  const taskStatuses = [
    { weight: 0.4, value: 'pending' },
    { weight: 0.3, value: 'in_progress' },
    { weight: 0.25, value: 'completed' },
    { weight: 0.05, value: 'cancelled' }
  ];

  for (let i = 0; i < count; i++) {
    const patient = faker.arrayElement(patients);
    const assignedTo = faker.arrayElement(providers);
    const taskType = faker.arrayElement(taskTypes);

    // Weighted status selection
    const rand = faker.random();
    let cumulativeWeight = 0;
    let status = 'pending';
    for (const statusOption of taskStatuses) {
      cumulativeWeight += statusOption.weight;
      if (rand <= cumulativeWeight) {
        status = statusOption.value;
        break;
      }
    }

    tasks.push({
      id: faker.uuid(),
      organization_id: orgId,
      title: `${taskType} for ${patient.first_name} ${patient.last_name}`,
      description: faker.paragraph(),
      assigned_to: assignedTo.id,
      patient_id: patient.id,
      priority: faker.arrayElement(priorities),
      status: status,
      due_date: faker.futureDate(30),
      completed_at: status === 'completed' ? faker.pastDate(7) : null,
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return tasks;
}

/**
 * Generate additional comprehensive data (claims, patient alerts)
 */
function generateAdditionalData(patients, providers) {
  // Generate claims
  const claims = [];
  for (let i = 0; i < 50; i++) {
    const patient = faker.arrayElement(patients);
    const provider = faker.arrayElement(providers);

    claims.push({
      id: faker.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      claim_number: faker.alphanumeric(10).toUpperCase(),
      service_date: faker.pastDate(1),
      diagnosis_codes: [faker.alphanumeric(5).toUpperCase()],
      procedure_codes: [faker.alphanumeric(5).toUpperCase()],
      amount_billed: faker.float(100, 5000, 2),
      amount_paid: faker.float(50, 4000, 2),
      status: faker.arrayElement(['submitted', 'processing', 'paid', 'denied']),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  // Generate patient alerts
  const alerts = [];
  for (let i = 0; i < 30; i++) {
    const patient = faker.arrayElement(patients);
    const provider = faker.arrayElement(providers);

    alerts.push({
      id: faker.uuid(),
      patient_id: patient.id,
      provider_id: provider.id,
      alert_type: faker.arrayElement(['allergy', 'medication_interaction', 'critical_value', 'follow_up_needed']),
      message: faker.sentence(),
      severity: faker.arrayElement(['low', 'medium', 'high', 'critical']),
      active: faker.boolean(0.8),
      acknowledged: faker.boolean(0.6),
      created_at: 'NOW()',
      updated_at: 'NOW()'
    });
  }

  return { claims, alerts };
}

/**
 * Convert data to SQL INSERT statements
 */
function generateSQLInserts(tableName, data) {
  if (!data || data.length === 0) return '';

  const columns = Object.keys(data[0]);
  const values = data.map(row => {
    const rowValues = columns.map(col => {
      const value = row[col];
      if (value === null || value === undefined) return 'NULL';
      if (value === 'NOW()') return 'NOW()';
      if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
      if (typeof value === 'boolean') return value;
      if (Array.isArray(value)) return `ARRAY[${value.map(v => `'${v}'`).join(', ')}]`;
      if (value instanceof Date) return `'${value.toISOString()}'`;
      return value;
    });
    return `(${rowValues.join(', ')})`;
  });

  return `
-- Insert ${tableName}
INSERT INTO public.${tableName} (${columns.join(', ')})
VALUES ${values.join(',\n       ')};
`;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting comprehensive seed data generation...');
  console.log('📊 This will create extensive realistic medical data for all organizations');

  // Get existing organizations from the database
  const organizations = [
    { id: 'd0954741-9386-4993-a0ab-3de01655903c', name: 'Spritely Medical Center' },
    { id: '2b8f7c3e-4a5d-4e6f-8b9c-1d2e3f4a5b6c', name: 'Spritely Community Clinic' },
    { id: '3c9e8d4f-5b6e-4f7a-9c0d-2e3f4a5b6c7d', name: 'Spritely Pediatrics' },
    { id: '4d0f9e5a-6c7f-5a8b-0d1e-3f4a5b6c7d8e', name: 'Dermatology Partners' },
    { id: '5e1a0f6b-7d8a-6b9c-1e2f-4a5b6c7d8e9f', name: 'Downtown Urgent Care' },
    { id: '6f2b1a7c-8e9b-7c0d-2f3a-5b6c7d8e9f0a', name: 'Eastside Community Hospital' },
    { id: '7a3c2b8d-9f0c-8d1e-3a4b-6c7d8e9f0a1b', name: 'Harbor View Hospital' },
    { id: '8b4d3c9e-0a1d-9e2f-4b5c-7d8e9f0a1b2c', name: 'Lakeside OB/GYN' },
    { id: '9c5e4d0f-1b2e-0f3a-5c6d-8e9f0a1b2c3d', name: 'Metropolitan Medical Center' },
    { id: '0d6f5e1a-2c3f-1a4b-6d7e-9f0a1b2c3d4e', name: 'Neurology Consultants' },
    { id: '1e7a6f2b-3d4a-2b5c-7e8f-0a1b2c3d4e5f', name: 'Northside Regional Hospital' },
    { id: '2f8b7a3c-4e5b-3c6d-8f9a-1b2c3d4e5f6a', name: 'Parkside Family Medicine' },
    { id: '3a9c8b4d-5f6c-4d7e-9a0b-2c3d4e5f6a7b', name: 'Riverside Health Center' },
    { id: '4b0d9c5e-6a7d-5e8f-0b1c-3d4e5f6a7b8c', name: 'Summit Orthopedic Specialists' },
    { id: '5c1e0d6f-7b8e-6f9a-1c2d-4e5f6a7b8c9d', name: 'Valley Cardiology Associates' },
    { id: '6d2f1e7a-8c9f-7a0b-2d3e-5f6a7b8c9d0e', name: 'Westlake Medical System' }
  ];

  let allSQL = `-- Comprehensive Seed Data Generated by Simple Node.js Generator
-- Generated on: ${new Date().toISOString()}
-- This file contains extensive realistic medical data for all organizations

BEGIN;

-- Clear existing comprehensive data (keep core reference data)
DELETE FROM public.clinical_notes;
DELETE FROM public.lab_results;
DELETE FROM public.immunizations;
DELETE FROM public.medications;
DELETE FROM public.claims;
DELETE FROM public.patient_alerts;
DELETE FROM public.tasks WHERE organization_id NOT IN (SELECT id FROM organizations WHERE name LIKE 'Spritely%');
DELETE FROM public.appointments WHERE organization_id NOT IN (SELECT id FROM organizations WHERE name LIKE 'Spritely%');
DELETE FROM public.medical_records WHERE organization_id NOT IN (SELECT id FROM organizations WHERE name LIKE 'Spritely%');
DELETE FROM public.healthcare_providers WHERE organization_id NOT IN (SELECT id FROM organizations WHERE name LIKE 'Spritely%');
DELETE FROM public.patients WHERE organization_id NOT IN (SELECT id FROM organizations WHERE name LIKE 'Spritely%');

`;

  let totalProviders = 0;
  let totalPatients = 0;
  let totalAppointments = 0;
  let totalMedicalRecords = 0;

  // Generate data for each organization
  for (const org of organizations) {
    console.log(`\n📋 Generating data for: ${org.name}`);

    // Determine data volume based on organization type
    const isMainOrg = org.name.includes('Spritely');
    const patientCount = isMainOrg ? faker.int(60, 80) : faker.int(25, 50);
    const providerCount = isMainOrg ? faker.int(6, 10) : faker.int(3, 6);

    console.log(`   👥 Generating ${patientCount} patients and ${providerCount} providers`);

    // Generate core data
    const providers = generateProviders(org.id, org.name, providerCount);
    const patients = generatePatients(org.id, patientCount);
    const medicalRecords = generateMedicalRecords(patients, providers, org.id, Math.floor(patientCount * 0.4));
    const appointments = generateAppointments(patients, providers, org.id, Math.floor(patientCount * 2.5));
    const medications = generateMedications(patients, providers, Math.floor(patientCount * 0.8));
    const labResults = generateLabResults(patients, providers, Math.floor(patientCount * 0.6));
    const immunizations = generateImmunizations(patients, providers, Math.floor(patientCount * 0.5));
    const tasks = generateTasks(patients, providers, org.id, Math.floor(patientCount * 0.7));
    const { claims, alerts } = generateAdditionalData(patients, providers);

    console.log(`   📊 Generated: ${appointments.length} appointments, ${medicalRecords.length} medical records, ${medications.length} medications`);
    console.log(`   🧪 Generated: ${labResults.length} lab results, ${immunizations.length} immunizations, ${tasks.length} tasks`);
    console.log(`   💼 Generated: ${claims.length} claims, ${alerts.length} patient alerts`);

    // Track totals
    totalProviders += providers.length;
    totalPatients += patients.length;
    totalAppointments += appointments.length;
    totalMedicalRecords += medicalRecords.length;

    // Add to SQL
    allSQL += `\n-- Data for ${org.name}\n`;
    allSQL += generateSQLInserts('healthcare_providers', providers);
    allSQL += generateSQLInserts('patients', patients);
    allSQL += generateSQLInserts('medical_records', medicalRecords);
    allSQL += generateSQLInserts('appointments', appointments);
    allSQL += generateSQLInserts('medications', medications);
    allSQL += generateSQLInserts('lab_results', labResults);
    allSQL += generateSQLInserts('immunizations', immunizations);
    allSQL += generateSQLInserts('tasks', tasks);
    allSQL += generateSQLInserts('claims', claims);
    allSQL += generateSQLInserts('patient_alerts', alerts);
  }

  allSQL += '\nCOMMIT;\n';

  // Write to file
  const outputPath = `${process.cwd()}/supabase/seeds/environments/development/99_comprehensive_data.sql`;
  fs.writeFileSync(outputPath, allSQL);

  console.log('\n✅ Comprehensive seed data generation complete!');
  console.log(`📊 Summary:`);
  console.log(`   🏥 Organizations: ${organizations.length}`);
  console.log(`   👨‍⚕️ Providers: ${totalProviders}`);
  console.log(`   👥 Patients: ${totalPatients}`);
  console.log(`   📅 Appointments: ${totalAppointments}`);
  console.log(`   📋 Medical Records: ${totalMedicalRecords}`);
  console.log(`📁 Generated file: ${outputPath}`);
  console.log(`💾 File size: ${(fs.statSync(outputPath).size / 1024 / 1024).toFixed(2)} MB`);
  console.log('🔄 Run "npx supabase db reset --local" to apply the new seed data');
}

// Run the script
main().catch(console.error);
